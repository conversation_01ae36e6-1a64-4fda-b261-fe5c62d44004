package com.tool.pams.api.web.controller.test;

import com.tool.pams.common.domain.dto.UserDTO;
import com.hzed.structure.common.util.AssertUtil;
import com.hzed.structure.common.util.IdUtil;
import com.hzed.structure.log.annotation.PrintLog;
import com.hzed.structure.secure.annotation.Authentication;
import com.hzed.structure.secure.annotation.CurrentUserId;
import com.hzed.structure.secure.util.SecureUtil;
import com.hzed.structure.tool.annotation.PubResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzed.structure.secure.annotation.CurrentUser;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 接口安全
 * @date 2021/7/23
 **/
@Slf4j
@Tag(name = "user", description = "用户")
@RestController
@RequestMapping("/secure")
@RequiredArgsConstructor
public class SecureController {

    @PubResponse
    @PrintLog(value = "用户登录")
    @GetMapping("/login")
    public String loginIn(String mobile) {
        AssertUtil.notNull(mobile, "手机号mobile不能为空");

        UserDTO redisUser = new UserDTO();
        redisUser.setId(888L);
        redisUser.setMobile(mobile);
        String token = IdUtil.getUUID();
        //登录成功
        SecureUtil.saveAuth(token, Long.toString(redisUser.getId()), redisUser);
        return token;
    }

    /**
     * 登录认证
     * http://localhost:8000/example-api/secure/index?token=e76ab78e890452519fa8795ac061221f
     *
     * @return
     */
    @PubResponse
    @PrintLog(value = "接口需要认证")
    @Authentication
    @GetMapping("/currentUserId")
    public Long userAccess(@CurrentUserId Long userId) {
        return userId;
    }


    @PubResponse
    @PrintLog(value = "接口需要认证2")
    @Authentication
    @GetMapping("/currentUser")
    public UserDTO crrentUser(@CurrentUser UserDTO userDTO) {
        return userDTO;
    }


    @Authentication
    @PrintLog("用户退出")
    @GetMapping("/loginOut")
    public void loginOut(@CurrentUserId Long userId) {
        log.info("当前用户退出系统：{}", userId);
        SecureUtil.deleteAuth();
    }

}
