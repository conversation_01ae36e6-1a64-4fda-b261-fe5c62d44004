package com.tool.pams.repository.domain.cmdb.bo;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 新业务系统资源信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 11:13:24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "PamsBusinessSystemInfoSaveBO对象", description = "新业务系统资源信息表")
public class PamsBusinessSystemInfoSaveBO implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "系统编码")
    private String systemNo;


    @Schema(description = "系统名称")
    private String systemName;


    @Schema(description = "系统代号")
    private String systemCode;


    @Schema(description = "系统类别")
    private String systemType;


    @Schema(description = "账号ID")
    private String accountId;


    @Schema(description = "负责人")
    private String director;


}
