package com.tool.pams.api.web.controller.test;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.tool.pams.common.domain.dto.UserDTO;
import com.tool.pams.repository.domain.db.Dict;
import com.tool.pams.repository.service.test.IDictService;
import com.hzed.structure.common.util.str.StringUtil;
import com.hzed.structure.log.annotation.PrintLog;
import com.hzed.structure.notice.util.WarnTemplateUtil;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.hzed.structure.tool.annotation.PubResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @date 2021/7/23
 **/
@Slf4j
@Tag(name = "user", description = "用户")
@RestController
@RequestMapping("/db")
@RequiredArgsConstructor
public class DbController {
    private final IDictService dictService;


    @PrintLog("查找")
    @PubResponse
    @GetMapping("/query")
    public List<Dict> query(String code) {
        if (StringUtil.isBlank(code)) {
            code = "education_type";
        }
        List<Dict> dictList = dictService.queryByCode(code);
        return dictList;
    }

    @PrintLog("分页")
    @PubResponse
    @GetMapping("/page")
    public Page<Dict> page() {
        Page pageDTO = PageDTO.of(1, 2);
        pageDTO = dictService.getPage(pageDTO, "profession");
        return pageDTO;
    }
}
