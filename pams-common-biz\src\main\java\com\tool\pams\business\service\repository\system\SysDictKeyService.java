package com.tool.pams.business.service.repository.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.pams.repository.domain.system.bo.SysDictKeyQueryParamsBO;
import com.tool.pams.repository.domain.system.bo.SysDictKeySaveBO;
import com.tool.pams.repository.domain.system.bo.SysDictKeyUpdateBO;
import com.tool.pams.repository.domain.system.db.SysDictKeyDO;
import com.tool.pams.repository.domain.system.vo.SysDictKeyDetailVO;
import com.tool.pams.repository.domain.system.vo.SysDictKeyPageVO;


/**
 * <p>
 * 字典 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface SysDictKeyService extends IService<SysDictKeyDO> {

    /**
     * 字典配置分页列表查询
     * @param bo 字典配置信息
     * @return 字典配置分页列表
     */
    IPage<SysDictKeyPageVO> selectPage(SysDictKeyQueryParamsBO bo);


    /**
     * 批量删除字典信息
     *
     * @param dictId 需要删除的字典ID
     * @return
     */
    Boolean deleteDictTypeByIds(Long dictId);

    /**
     * 修改字典信息
     * @param dict
     * @return
     */
    Boolean updateDictKey(SysDictKeyUpdateBO dict);

    /**
     * 校验字典类型称是否唯一
     *
     * @param dictKey 字典类型
     * @return 结果
     */
    boolean checkDictTypeUnique(SysDictKeyDO dictKey);

    /**
     * 新增字典信息
     * @param dict
     * @return
     */
    Boolean saveDictKey(SysDictKeySaveBO dict);

    /**
     * 根据id查询字典信息
     * @param id
     * @return
     */
    SysDictKeyDetailVO getInfo(Long id);
}
