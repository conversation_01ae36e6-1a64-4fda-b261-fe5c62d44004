package com.tool.pams.repository.mapper.cmdb;

import com.tool.pams.repository.domain.cmdb.db.PamsAppSummaryInfoDO;
import com.tool.pams.repository.domain.cmdb.vo.PamsAppSummaryInfoDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsAppSummaryInfoPageVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 应用信息汇总表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Mapper
public interface PamsAppSummaryInfoMapper extends BaseMapper<PamsAppSummaryInfoDO> {

    /**
     * 批量插入或更新应用汇总信息（使用INSERT ... ON DUPLICATE KEY UPDATE）
     *
     * @param records 应用汇总信息列表
     * @return 受影响的行数
     */
    int insertOrUpdateBatch(@Param("records") List<PamsAppSummaryInfoDO> records);

    /**
     * 根据ID查询应用汇总信息详情（包含关联信息）
     *
     * @param id 主键ID
     * @return 应用汇总信息详情
     */
    PamsAppSummaryInfoDetailVO selectDetailById(@Param("id") Long id);

    /**
     * 分页查询应用汇总信息（包含关联信息）
     *
     * @param page 分页参数
     * @param queryParamsBO 查询条件
     * @return 分页结果
     */
    IPage<PamsAppSummaryInfoPageVO> selectPageWithRelation(IPage<PamsAppSummaryInfoPageVO> page, @Param("query") com.tool.pams.repository.domain.cmdb.bo.PamsAppSummaryInfoQueryParamsBO queryParamsBO);


}
