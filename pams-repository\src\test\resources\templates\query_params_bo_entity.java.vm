package ${boPath};

#foreach($pkg in ${table.importPackages})
#if(${pkg.indexOf("mybatisplus")}==-1)
import ${pkg};
#end
#end
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.pams.repository.domain.common.PageParamsBO;
import ${package.Entity}.${entity};
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * $!{table.comment}
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "${queryParamsBO}对象", description = "$!{table.comment}")
public class ${queryParamsBO} extends PageParamsBO<${entity}> implements Serializable{

    private static final long serialVersionUID = 1L;

## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})
    #if($field.propertyType=="LocalDateTime")

        #if("$!field.comment" != "")
    @Schema(description = "${field.comment}-开始")
        #end
    private ${field.propertyType} ${field.propertyName}Start;

        #if("$!field.comment" != "")
    @Schema(description = "${field.comment}-结束")
        #end
    private ${field.propertyType} ${field.propertyName}End;
    #else

        #if("$!field.comment" != "")
    @Schema(description = "${field.comment}")
        #end
    private ${field.propertyType} ${field.propertyName};
    #end
#end

    @Override
    public LambdaQueryWrapper<${entity}> queryWrapper() {

        LambdaQueryWrapper<${entity}> query = new LambdaQueryWrapper<>();

#foreach($field in ${table.fields})
    #if($field.propertyType=="String")
        query.eq(StringUtils.isNotBlank(${field.propertyName}),${entity}::get${field.propertyName.substring(0, 1).toUpperCase()}${field.propertyName.substring(1)},${field.propertyName});

    #elseif($field.propertyType=="Integer"||$field.propertyType=="Long"||$field.propertyType=="Boolean")
        query.eq(${field.propertyName}!=null,${entity}::get${field.propertyName.substring(0, 1).toUpperCase()}${field.propertyName.substring(1)},${field.propertyName});

    #elseif($field.propertyType=="LocalDateTime")
        query.ge(${field.propertyName}Start != null, ${entity}::get${field.propertyName.substring(0, 1).toUpperCase()}${field.propertyName.substring(1)}, ${field.propertyName}Start);

        query.le(${field.propertyName}End != null, ${entity}::get${field.propertyName.substring(0, 1).toUpperCase()}${field.propertyName.substring(1)}, ${field.propertyName}End);

    #end
#end
        return query;
    }
}
