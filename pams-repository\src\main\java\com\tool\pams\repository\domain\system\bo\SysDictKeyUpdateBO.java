package com.tool.pams.repository.domain.system.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <p>
 * 字典
 * </p>
 * <AUTHOR>
 * @since 2025-01-08 14:20:09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SysDictKeyUpdateBO对象", description = "字典字典")
public class SysDictKeyUpdateBO implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "id")
    private Long id;

    @Schema(description = "字典编码")
    @NotBlank(message = "字典编码不能为空")
    private String keyName;

    @NotBlank(message = "字典名称不能为空")
    @Schema(description = "字典名称")
    private String keyLabel;

    @Schema(description = "字典描述")
    private String description;

}
