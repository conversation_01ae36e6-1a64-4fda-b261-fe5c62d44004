package com.tool.pams.repository.domain.system.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 字典配置值
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08 14:20:27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SysDictValueUpdateBO对象", description = "字典配置值")
public class SysDictValueUpdateBO implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "id")
    private Long id;

    @Schema(description = "字典id")
    @NotNull(message = "字典id不能为空")
    private Long keyId;

    @Schema(description = "字典值")
    @NotBlank(message = "字典值不能为空")
    private String value;

    @Schema(description = "字典项")
    @NotBlank(message = "字典项不能为空")
    private String label;

    @Schema(description = "说明")
    private String description;


    @Schema(description = "排序")
    private Integer sort;

}
