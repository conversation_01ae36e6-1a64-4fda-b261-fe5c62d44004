server:
  port: 8888

spring:
  datasource:
    dynamic:
      primary: pams #设置默认的数据源或者数据源组,默认值即为 master
      strict: false # 设置严格模式，当数据源找不到时，是否抛出异常，默认为false不抛出
      hikari:
        pool-name: PrimaryHikariCP
        minimum-idle: 2
        maximum-pool-size: 10
        idle-timeout: 30000
        connection-timeout: 30000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
        validation-timeout: 5000
        leak-detection-threshold: 60000
        initialization-fail-timeout: 10000
      datasource:
        pams:
          type: com.zaxxer.hikari.HikariDataSource
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *********************************************************************************************************************************************************************************
          username: u_pams
          password: Puincn$cnsijf3mchnfei232
  redis:
    database: 237
    host: redis-local.qmwallet.vip
    port: 4467
    password: smu72fjs9bbshzp
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        min-idle: 1
        max-idle: 8
        max-wait: 3000ms
        shutdown-timeout: 5000ms
      cluster:
        refresh:
          adaptive: true
          period: 10000ms

# springdoc-openapi项目配置
springdoc:
  swagger-ui:
    enabled: true

hzed:
  sso:
    # todo:修改systemDomainCode
    systemDomainCode: test-manage-admin
    sso-service-url: https://sso.qmwallet.vip/sso-service
    access-denied-handle-type: json
    filter-chain-definition-map[/pubLogSql/save]: anon

