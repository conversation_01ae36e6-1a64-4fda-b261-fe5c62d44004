<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.pams.repository.mapper.cmdb.PamsBusinessSystemInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemInfoDO">
        <id column="id" property="id" />
        <result column="system_no" property="systemNo" />
        <result column="system_name" property="systemName" />
        <result column="system_code" property="systemCode" />
        <result column="system_type" property="systemType" />
        <result column="account_id" property="accountId" />
        <result column="director" property="director" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, system_no, system_name, system_code, system_type, account_id, director, create_time, update_time, creator, updater, deleted
    </sql>

    <select id="syncBusinessSystemInfo" resultType="com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemInfoDO">
        SELECT
            s.id,
            s.create_time,
            s.update_time,
            s.is_delete  AS deleted,
            s.version,
            s.system_no,
            s.system_name,
            s.system_code,
            s.system_type,
            s.account_id
        FROM
            t_cmdb_business_system_info_new s
        order by s.create_time
    </select>


    <insert id="insertOrUpdateBatch">
        INSERT INTO t_pams_business_system_info (
            id, system_no, system_name, system_code, system_type, account_id, director, create_time, update_time, creator, updater, deleted
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.id}, #{record.systemNo}, #{record.systemName}, #{record.systemCode}, #{record.systemType},
                #{record.accountId}, #{record.director}, #{record.createTime}, #{record.updateTime}, #{record.creator},
                #{record.updater}, #{record.deleted}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            system_no = VALUES(system_no),
            system_name = VALUES(system_name),
            system_code = VALUES(system_code),
            system_type = VALUES(system_type),
            account_id = VALUES(account_id),
            director = VALUES(director),
            update_time = VALUES(update_time),
            updater = VALUES(updater),
            deleted = VALUES(deleted)
    </insert>
</mapper>
