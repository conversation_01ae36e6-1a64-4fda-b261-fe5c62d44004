package com.tool.pams.repository.domain.db;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_dict")
public class Dict implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 编码
     */
    @TableField("dict_code")
    private String dictCode;

    /**
     * 值
     */
    @TableField("dict_value")
    private String dictValue;

    /**
     * 名称
     */
    @TableField("dict_name")
    private String dictName;

    /**
     * 逻辑删除：1 - 删除；0 - 未删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 描述
     */
    @TableField("dict_desc")
    private String dictDesc;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
