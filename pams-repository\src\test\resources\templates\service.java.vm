package ${package.Service};

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import ${package.Entity}.${entity};
import ${boPath}.${saveBO};
import ${boPath}.${updateBO};
import ${boPath}.${queryParamsBO};
import ${voPath}.${detailVO};
import ${voPath}.${pageVO};

/**
 * <p>
 * $!{table.comment} 服务类
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
public interface ${table.serviceName} extends IService<${entity}> {

    /**
     * 添加信息
     *
     * @param saveBO
     * @return
     */
    Boolean saveInfo(${saveBO} saveBO);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delInfo(Long id);

    /**
     * 修改信息
     *
     * @param updateBO
     * @return
     */
    Boolean updateInfo(${updateBO} updateBO);

    /**
     * 通过ID获取信息
     *
     * @param id
     * @return
     */
    ${detailVO} getInfo(Long id);

    /**
     * 分页获取列表
     *
     * @param queryParamsBO
     * @return
     */
    IPage<${pageVO}> getPageInfo(${queryParamsBO} queryParamsBO);

}
