<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.pams.repository.mapper.cmdb.PamsAppSummaryInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.pams.repository.domain.cmdb.db.PamsAppSummaryInfoDO">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="app_name" property="appName" />
        <result column="status" property="status" />
        <result column="type" property="type" />
        <result column="organization_id" property="organizationId" />
        <result column="module_count" property="moduleCount" />
        <result column="total_publish_count" property="totalPublishCount" />
        <result column="total_publish_duration" property="totalPublishDuration" />
        <result column="avg_publish_duration" property="avgPublishDuration" />
        <result column="avg_startup_duration" property="avgStartupDuration" />
        <result column="dev_team" property="devTeam" />
        <result column="prod_app_identifier" property="prodAppIdentifier" />
        <result column="git_address" property="gitAddress" />
        <result column="develop_language" property="developLanguage" />
        <result column="test_team" property="testTeam" />
        <result column="project_manager" property="projectManager" />
        <result column="app_major_category" property="appMajorCategory" />
        <result column="app_minor_category" property="appMinorCategory" />
        <result column="sso_system_flag" property="ssoSystemFlag" />
        <result column="source_project_flag" property="sourceProjectFlag" />
        <result column="database_flag" property="databaseFlag" />
        <result column="oss_private_bucket_flag" property="ossPrivateBucketFlag" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_id, app_name, status, type, organization_id, module_count, total_publish_count, 
        total_publish_duration, avg_publish_duration, avg_startup_duration, dev_team, 
        prod_app_identifier, git_address, develop_language, test_team, project_manager, 
        app_major_category, app_minor_category, sso_system_flag, source_project_flag, 
        database_flag, oss_private_bucket_flag, create_time, update_time, creator, updater, deleted
    </sql>

    <!-- 详情查询结果映射 -->
    <resultMap id="DetailResultMap" type="com.tool.pams.repository.domain.cmdb.vo.PamsAppSummaryInfoDetailVO">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="app_name" property="appName" />
        <result column="status" property="status" />
        <result column="type" property="type" />
        <result column="organization_id" property="organizationId" />
        <result column="module_count" property="moduleCount" />
        <result column="total_publish_count" property="totalPublishCount" />
        <result column="total_publish_duration" property="totalPublishDuration" />
        <result column="avg_publish_duration" property="avgPublishDuration" />
        <result column="avg_startup_duration" property="avgStartupDuration" />
        <result column="dev_team" property="devTeam" />
        <result column="prod_app_identifier" property="prodAppIdentifier" />
        <result column="git_address" property="gitAddress" />
        <result column="develop_language" property="developLanguage" />
        <result column="test_team" property="testTeam" />
        <result column="project_manager" property="projectManager" />
        <result column="app_major_category" property="appMajorCategory" />
        <result column="app_minor_category" property="appMinorCategory" />
        <result column="sso_system_flag" property="ssoSystemFlag" />
        <result column="source_project_flag" property="sourceProjectFlag" />
        <result column="database_flag" property="databaseFlag" />
        <result column="oss_private_bucket_flag" property="ossPrivateBucketFlag" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
        <result column="organization_name" property="organizationName" />
        <result column="product_names" property="productNames" />
    </resultMap>

    <!-- 分页查询结果映射 -->
    <resultMap id="PageResultMap" type="com.tool.pams.repository.domain.cmdb.vo.PamsAppSummaryInfoPageVO">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="app_name" property="appName" />
        <result column="status" property="status" />
        <result column="type" property="type" />
        <result column="organization_id" property="organizationId" />
        <result column="module_count" property="moduleCount" />
        <result column="total_publish_count" property="totalPublishCount" />
        <result column="total_publish_duration" property="totalPublishDuration" />
        <result column="avg_publish_duration" property="avgPublishDuration" />
        <result column="avg_startup_duration" property="avgStartupDuration" />
        <result column="dev_team" property="devTeam" />
        <result column="prod_app_identifier" property="prodAppIdentifier" />
        <result column="git_address" property="gitAddress" />
        <result column="develop_language" property="developLanguage" />
        <result column="test_team" property="testTeam" />
        <result column="project_manager" property="projectManager" />
        <result column="app_major_category" property="appMajorCategory" />
        <result column="app_minor_category" property="appMinorCategory" />
        <result column="sso_system_flag" property="ssoSystemFlag" />
        <result column="source_project_flag" property="sourceProjectFlag" />
        <result column="database_flag" property="databaseFlag" />
        <result column="oss_private_bucket_flag" property="ossPrivateBucketFlag" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="organization_name" property="organizationName" />
        <result column="product_names" property="productNames" />
    </resultMap>

    <!-- 根据ID查询详情（包含关联信息） -->
    <select id="selectDetailById" resultMap="DetailResultMap">
        SELECT
            asi.*,
            org.organization_name,
            GROUP_CONCAT(DISTINCT bsi.system_name SEPARATOR ', ') as product_names
        FROM t_pams_app_summary_info asi
        LEFT JOIN t_organization org ON asi.organization_id = org.id AND org.deleted = 0
        LEFT JOIN t_pams_business_system_app_relation bsar ON asi.app_id = bsar.app_id AND bsar.deleted = 0
        LEFT JOIN t_pams_business_system_info bsi ON bsar.business_system_id = bsi.id AND bsi.deleted = 0
        WHERE asi.id = #{id} AND asi.deleted = 0
        GROUP BY asi.id
    </select>

    <!-- 分页查询（包含关联信息） -->
    <select id="selectPageWithRelation" resultMap="PageResultMap">
        SELECT
            asi.*,
            org.organization_name,
            GROUP_CONCAT(DISTINCT bsi.system_name SEPARATOR ', ') as product_names
        FROM t_pams_app_summary_info asi
        LEFT JOIN t_organization org ON asi.organization_id = org.id AND org.deleted = 0
        LEFT JOIN t_pams_business_system_app_relation bsar ON asi.app_id = bsar.app_id AND bsar.deleted = 0
        LEFT JOIN t_pams_business_system_info bsi ON bsar.business_system_id = bsi.id AND bsi.deleted = 0
        WHERE asi.deleted = 0
        <if test="query.id != null">
            AND asi.id = #{query.id}
        </if>
        <if test="query.appId != null">
            AND asi.app_id = #{query.appId}
        </if>
        <if test="query.appName != null and query.appName != ''">
            AND asi.app_name = #{query.appName}
        </if>
        <if test="query.status != null and query.status != ''">
            AND asi.status = #{query.status}
        </if>
        <if test="query.type != null and query.type != ''">
            AND asi.type = #{query.type}
        </if>
        <if test="query.organizationId != null">
            AND asi.organization_id = #{query.organizationId}
        </if>
        <if test="query.organizationName != null and query.organizationName != ''">
            AND org.organization_name = #{query.organizationName}
        </if>
        <if test="query.productName != null and query.productName != ''">
            AND bsi.system_name = #{query.productName}
        </if>
        GROUP BY asi.id
        ORDER BY asi.create_time DESC
    </select>



    <!-- 批量插入或更新 -->
    <insert id="insertOrUpdateBatch">
        INSERT INTO t_pams_app_summary_info (
            id, app_id, app_name, status, type, organization_id, module_count, total_publish_count,
            total_publish_duration, avg_publish_duration, avg_startup_duration, dev_team,
            prod_app_identifier, git_address, develop_language, test_team, project_manager,
            app_major_category, app_minor_category, sso_system_flag, source_project_flag,
            database_flag, oss_private_bucket_flag, create_time, update_time, creator, updater, deleted
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.id}, #{record.appId}, #{record.appName}, #{record.status}, #{record.type},
                #{record.organizationId}, #{record.moduleCount}, #{record.totalPublishCount},
                #{record.totalPublishDuration}, #{record.avgPublishDuration}, #{record.avgStartupDuration},
                #{record.devTeam}, #{record.prodAppIdentifier}, #{record.gitAddress}, #{record.developLanguage},
                #{record.testTeam}, #{record.projectManager}, #{record.appMajorCategory}, #{record.appMinorCategory},
                #{record.ssoSystemFlag}, #{record.sourceProjectFlag}, #{record.databaseFlag},
                #{record.ossPrivateBucketFlag}, #{record.createTime}, #{record.updateTime}, #{record.creator},
                #{record.updater}, #{record.deleted}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            app_name = VALUES(app_name),
            status = VALUES(status),
            type = VALUES(type),
            organization_id = VALUES(organization_id),
            module_count = VALUES(module_count),
            total_publish_count = VALUES(total_publish_count),
            total_publish_duration = VALUES(total_publish_duration),
            avg_publish_duration = VALUES(avg_publish_duration),
            avg_startup_duration = VALUES(avg_startup_duration),
            dev_team = VALUES(dev_team),
            prod_app_identifier = VALUES(prod_app_identifier),
            git_address = VALUES(git_address),
            develop_language = VALUES(develop_language),
            test_team = VALUES(test_team),
            project_manager = VALUES(project_manager),
            app_major_category = VALUES(app_major_category),
            app_minor_category = VALUES(app_minor_category),
            sso_system_flag = VALUES(sso_system_flag),
            source_project_flag = VALUES(source_project_flag),
            database_flag = VALUES(database_flag),
            oss_private_bucket_flag = VALUES(oss_private_bucket_flag),
            update_time = VALUES(update_time),
            updater = VALUES(updater),
            deleted = VALUES(deleted)
    </insert>

</mapper>
