package com.tool.pams.job.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.hzed.structure.log.annotation.TraceId;
import com.hzed.structure.log.util.MdcUtil;
import com.tool.pams.business.service.repository.cmdb.PamsBusinessSystemInfoService;
import com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemAppRelationDO;
import com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemInfoDO;
import com.tool.pams.repository.domain.cmdb.db.PamsJenkinsPublishReportDO;
import com.tool.pams.repository.domain.cmdb.db.PamsSystemAppInfoDO;
import com.tool.pams.repository.mapper.cmdb.PamsBusinessSystemAppRelationMapper;
import com.tool.pams.repository.mapper.cmdb.PamsBusinessSystemInfoMapper;
import com.tool.pams.repository.mapper.cmdb.PamsJenkinsPublishReportMapper;
import com.tool.pams.repository.mapper.cmdb.PamsSystemAppInfoMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 同步CMDB
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SyncCmdbService {

    @Resource
    private PamsJenkinsPublishReportMapper pamsAutoPublishRecordMapper;

    @Resource
    private PamsSystemAppInfoMapper pamsSystemAppInfoMapper;

    @Resource
    private PamsBusinessSystemInfoMapper pamsBusinessSystemInfoMapper;

    @Resource
    private PamsBusinessSystemAppRelationMapper pamsBusinessSystemAppRelationMapper;

    /**
     * cmdb相关数据同步
     */
    @TraceId("cmdb相关数据同步")
    @XxlJob("syncCmdbHandler")
    public ReturnT<String> syncCmdbHandler() {
        XxlJobLogger.log("cmdb相关数据同步开始. traceId:{}", MdcUtil.getTrace());
        ReturnT<String> returnT = ReturnT.SUCCESS;

        try {
            List<PamsJenkinsPublishReportDO> pamsJenkinsPublishReportDos = pamsAutoPublishRecordMapper.syncCmdbPublishRecord(LocalDateTime.now().minusDays(900), LocalDateTime.now());
            if (CollectionUtil.isNotEmpty(pamsJenkinsPublishReportDos)) {
                pamsAutoPublishRecordMapper.insertOrUpdateBatch(pamsJenkinsPublishReportDos);
            }
        } catch (Exception e) {
            log.error("同步生产发布记录异常：{}", e);
            returnT = ReturnT.FAIL;
        }

        try {
            List<PamsSystemAppInfoDO> pamsSystemAppInfoDos = pamsSystemAppInfoMapper.syncSystemAppInfo();
            if (CollectionUtil.isNotEmpty(pamsSystemAppInfoDos)) {
                pamsSystemAppInfoMapper.insertOrUpdateBatch(pamsSystemAppInfoDos);
            }
        } catch (Exception e) {
            log.error("同步生产应用异常：{}", e);
            returnT = ReturnT.FAIL;
        }

        try {
            List<PamsBusinessSystemInfoDO> pamsBusinessSystemInfoDos = pamsBusinessSystemInfoMapper.syncBusinessSystemInfo();
            if (CollectionUtil.isNotEmpty(pamsBusinessSystemInfoDos)) {
                pamsBusinessSystemInfoMapper.insertOrUpdateBatch(pamsBusinessSystemInfoDos);
            }
        } catch (Exception e) {
            log.error("同步生产系统异常：{}", e);
            returnT = ReturnT.FAIL;

        }

        try {
            List<PamsBusinessSystemAppRelationDO> pamsBusinessSystemAppRelationDos = pamsBusinessSystemAppRelationMapper.syncPamsBusinessSystemAppRelation();
            if (CollectionUtil.isNotEmpty(pamsBusinessSystemAppRelationDos)) {
                pamsBusinessSystemAppRelationMapper.insertOrUpdateBatch(pamsBusinessSystemAppRelationDos);
            }
        } catch (Exception e) {
            log.error("同步系统应用关联异常：{}", e);
            returnT = ReturnT.FAIL;
        }
        XxlJobLogger.log("cmdb相关数据同步结束");

        return returnT;
    }

}
