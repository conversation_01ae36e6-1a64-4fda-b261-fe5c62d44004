<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>pams</artifactId>
        <groupId>com.tool.pams</groupId>
        <version>1.0.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>pams-common</artifactId>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>


    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzed.structure</groupId>
            <artifactId>structure-tool</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzed.structure</groupId>
            <artifactId>structure-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzed.structure</groupId>
            <artifactId>structure-notice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzed.structure</groupId>
            <artifactId>structure-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzed.structure</groupId>
            <artifactId>structure-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzed.structure</groupId>
            <artifactId>structure-async</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzed.sso</groupId>
            <artifactId>sso-client-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzed.structure</groupId>
            <artifactId>structure-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>
    </dependencies>

</project>
