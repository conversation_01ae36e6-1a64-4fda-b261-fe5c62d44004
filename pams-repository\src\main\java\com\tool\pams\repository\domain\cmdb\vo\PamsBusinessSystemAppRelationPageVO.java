package com.tool.pams.repository.domain.cmdb.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemAppRelationDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * 业务系统与应用关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 15:56:15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "PamsBusinessSystemAppRelationPageVO对象", description = "业务系统与应用关联表")
public class PamsBusinessSystemAppRelationPageVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @Schema(description = "唯一ID")
    private Long id;

    @Schema(description = "业务系统id")
    private Long businessSystemId;

    @Schema(description = "应用id")
    private Long appId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    public static PamsBusinessSystemAppRelationPageVO of(PamsBusinessSystemAppRelationDO entity){
        if(entity == null){
            return null;
        }
        PamsBusinessSystemAppRelationPageVO pageVO = new PamsBusinessSystemAppRelationPageVO();
        BeanUtils.copyProperties(entity,pageVO);
        return pageVO;
    }

}
