package com.tool.pams.business.service.repository.system.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.hzed.structure.common.exception.ServiceException;
import com.tool.pams.business.service.repository.system.SysDictKeyService;
import com.tool.pams.repository.domain.system.bo.SysDictKeyQueryParamsBO;
import com.tool.pams.repository.domain.system.bo.SysDictKeySaveBO;
import com.tool.pams.repository.domain.system.bo.SysDictKeyUpdateBO;
import com.tool.pams.repository.domain.system.db.SysDictKeyDO;
import com.tool.pams.repository.domain.system.db.SysDictValueDO;
import com.tool.pams.repository.domain.system.vo.SysDictKeyDetailVO;
import com.tool.pams.repository.domain.system.vo.SysDictKeyPageVO;
import com.tool.pams.repository.mapper.system.SysDictKeyMapper;
import com.tool.pams.repository.mapper.system.SysDictValueMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 字典字典 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Service
public class SysDictKeyServiceImpl extends ServiceImpl<SysDictKeyMapper, SysDictKeyDO> implements SysDictKeyService {

    @Autowired
    SysDictValueMapper dictValueMapper;

    @Override
    public IPage<SysDictKeyPageVO> selectPage(SysDictKeyQueryParamsBO bo) {
        return baseMapper.selectPage(bo.pageInfo(), bo.queryWrapper()).convert(SysDictKeyPageVO :: new);
    }

    @Override
    public Boolean deleteDictTypeByIds(Long dictId) {
        boolean exists = dictValueMapper.exists(new LambdaQueryWrapper<SysDictValueDO>()
                .eq(SysDictValueDO::getKeyId, dictId));
        if (exists) {
         throw new ServiceException("字典字典已分配,不能删除");
        }
        return SqlHelper.retBool(baseMapper.deleteById(dictId));
    }

    @Override
    public Boolean updateDictKey(SysDictKeyUpdateBO dict) {
        SysDictKeyDO sysDictKeyDO = new SysDictKeyDO();
        BeanUtils.copyProperties(dict, sysDictKeyDO);
        if (checkDictTypeUnique(sysDictKeyDO)) {
            throw new ServiceException("字典编码已存在");
        }
        SysDictKeyDO record = baseMapper.selectById(dict.getId());
        if (!record.getKeyName().equals(sysDictKeyDO.getKeyName())) {
            throw new ServiceException("字典编码不允许修改");
        }
        return SqlHelper.retBool(baseMapper.updateById(sysDictKeyDO));
    }

    @Override
    public boolean checkDictTypeUnique(SysDictKeyDO dictKey) {
        return baseMapper.exists(new LambdaQueryWrapper<SysDictKeyDO>()
                .eq(SysDictKeyDO::getKeyName, dictKey.getKeyName())
                .ne(ObjectUtil.isNotNull(dictKey.getId()), SysDictKeyDO::getId, dictKey.getId()));
    }

    @Override
    public Boolean saveDictKey(SysDictKeySaveBO dict) {
        SysDictKeyDO sysDictKeyDO = new SysDictKeyDO();
        BeanUtils.copyProperties(dict, sysDictKeyDO);
        if (checkDictTypeUnique(sysDictKeyDO)) {
            throw new ServiceException("字典编码已存在");
        }
        return SqlHelper.retBool(baseMapper.insert(sysDictKeyDO));
    }

    @Override
    public SysDictKeyDetailVO getInfo(Long id) {
        SysDictKeyDO sysDictKeyDO = baseMapper.selectById(id);
        SysDictKeyDetailVO sysDictKeyDetail = SysDictKeyDetailVO.of(sysDictKeyDO);
        List<SysDictValueDO> sysDictValues = dictValueMapper.selectList(new LambdaQueryWrapper<SysDictValueDO>()
                .eq(SysDictValueDO::getKeyId, id));
        sysDictKeyDetail.setDictValueList(sysDictValues);
        return sysDictKeyDetail;
    }

}
