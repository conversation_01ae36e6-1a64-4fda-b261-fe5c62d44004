package com.tool.pams.repository.service.test.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tool.pams.repository.domain.db.Dict;
import com.tool.pams.repository.mapper.test.DictMapper;
import com.tool.pams.repository.service.test.IDictService;
import com.hzed.structure.common.util.str.StringUtil;
import org.springframework.stereotype.Service;
import com.alicp.jetcache.anno.Cached;
import com.alicp.jetcache.anno.CacheType;

import java.util.concurrent.TimeUnit;
import java.util.List;

/**
 * <p>
 * 数据字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-22
 */
@Service
public class DictServiceImpl extends ServiceImpl<DictMapper, Dict> implements IDictService {

    @Override
    @Cached(name = "cached::smsTemplate:", key = "#code", cacheType = CacheType.REMOTE, expire = 6, timeUnit = TimeUnit.HOURS)
    public List<Dict> queryByCode(String code) {
        LambdaQueryWrapper<Dict> eq = Wrappers.lambdaQuery(Dict.class)
                .eq(StringUtil.isNotBlank(code), Dict::getDictCode, code);
        return this.list(eq);
    }


    @Override
    public Page<Dict> getPage(Page<Dict> pageDTO, String code) {
        LambdaQueryWrapper<Dict> eq = Wrappers.lambdaQuery(Dict.class)
                .eq(Dict::getDictCode, code);
        pageDTO = this.page(pageDTO, eq);
        return pageDTO;
    }
}
