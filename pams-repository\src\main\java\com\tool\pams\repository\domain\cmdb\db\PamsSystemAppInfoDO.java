package com.tool.pams.repository.domain.cmdb.db;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 系统应用信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 11:13:24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_pams_system_app_info")
@Schema(name = "PamsSystemAppInfoDO对象", description = "系统应用信息表")
public class PamsSystemAppInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId(value = "`id`",type = IdType.INPUT)
    private Long id;

    @Schema(description = "父级应用id")
    @TableField("`parent_app_id`")
    private Long parentAppId;

    @Schema(description = "应用编号")
    @TableField("`app_no`")
    private String appNo;

    @Schema(description = "应用类别")
    @TableField("`app_type`")
    private String appType;

    @Schema(description = "开发语言")
    @TableField("`develop_language`")
    private String developLanguage;

    @Schema(description = "账号ID")
    @TableField("`account_id`")
    private String accountId;

    @Schema(description = "应用名称")
    @TableField("`app_name`")
    private String appName;

    @Schema(description = "应用代号")
    @TableField("`app_code`")
    private String appCode;

    @Schema(description = "开发负责人")
    @TableField("`develop_director`")
    private String developDirector;

    @Schema(description = "GitLab地址")
    @TableField("`gitlab_address`")
    private String gitlabAddress;

    @Schema(description = "Jenkins地址")
    @TableField("`jenkins_address`")
    private String jenkinsAddress;

    @Schema(description = "部署端口")
    @TableField("`deploy_port`")
    private Integer deployPort;

    @Schema(description = "部署路径")
    @TableField("`deploy_path`")
    private String deployPath;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;

    @Schema(description = "说明")
    @TableField("`remark`")
    private String remark;


}
