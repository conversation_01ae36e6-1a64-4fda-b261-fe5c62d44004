package com.tool.pams.repository.domain.cmdb.db;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 应用信息汇总表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_pams_app_summary_info")
@Schema(name = "PamsAppSummaryInfoDO对象", description = "应用信息汇总表")
public class PamsAppSummaryInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId(value = "`id`", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "应用ID（对应t_pams_system_app_info的id，parent_app_id=0的父级节点）")
    @TableField("`app_id`")
    private Long appId;

    @Schema(description = "应用名称")
    @TableField("`app_name`")
    private String appName;

    @Schema(description = "状态（start=启用/stop=下架/maintain=维护中）")
    @TableField("`status`")
    private String status;

    @Schema(description = "类型（app=APP/frontend=前端/backend=后端，手动编辑）")
    @TableField("`type`")
    private String type;

    @Schema(description = "所属机构ID（手动编辑，关联查询）")
    @TableField("`organization_id`")
    private Long organizationId;

    @Schema(description = "模块数量")
    @TableField("`module_count`")
    private Integer moduleCount;

    @Schema(description = "总发布次数")
    @TableField("`total_publish_count`")
    private Integer totalPublishCount;

    @Schema(description = "总发布时长（毫秒）")
    @TableField("`total_publish_duration`")
    private Long totalPublishDuration;

    @Schema(description = "平均发布时长（毫秒）")
    @TableField("`avg_publish_duration`")
    private Long avgPublishDuration;

    @Schema(description = "平均启动时长（毫秒）")
    @TableField("`avg_startup_duration`")
    private Long avgStartupDuration;

    @Schema(description = "负责开发组")
    @TableField("`dev_team`")
    private String devTeam;

    @Schema(description = "生产应用标识")
    @TableField("`prod_app_identifier`")
    private String prodAppIdentifier;

    @Schema(description = "git地址")
    @TableField("`git_address`")
    private String gitAddress;

    @Schema(description = "开发语言")
    @TableField("`develop_language`")
    private String developLanguage;

    @Schema(description = "负责测试组（手动编辑）")
    @TableField("`test_team`")
    private String testTeam;

    @Schema(description = "负责项目经理（手动编辑）")
    @TableField("`project_manager`")
    private String projectManager;

    @Schema(description = "应用大类（手动编辑）")
    @TableField("`app_major_category`")
    private String appMajorCategory;

    @Schema(description = "应用小类（手动编辑）")
    @TableField("`app_minor_category`")
    private String appMinorCategory;

    @Schema(description = "SSO系统标识（手动编辑）")
    @TableField("`sso_system_flag`")
    private String ssoSystemFlag;

    @Schema(description = "源码工程标识（手动编辑）")
    @TableField("`source_project_flag`")
    private String sourceProjectFlag;

    @Schema(description = "数据库标识（手动编辑）")
    @TableField("`database_flag`")
    private String databaseFlag;

    @Schema(description = "OSS私有文件桶标识（手动编辑）")
    @TableField("`oss_private_bucket_flag`")
    private String ossPrivateBucketFlag;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;
}
