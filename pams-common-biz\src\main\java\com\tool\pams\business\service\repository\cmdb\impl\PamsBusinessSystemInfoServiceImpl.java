package com.tool.pams.business.service.repository.cmdb.impl;

import com.tool.pams.repository.domain.cmdb.bo.PamsBusinessSystemInfoSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsBusinessSystemInfoUpdateBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsBusinessSystemInfoQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.vo.PamsBusinessSystemInfoDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsBusinessSystemInfoPageVO;
import com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemInfoDO;
import com.tool.pams.repository.mapper.cmdb.PamsBusinessSystemInfoMapper;
import com.tool.pams.business.service.repository.cmdb.PamsBusinessSystemInfoService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 新业务系统资源信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 11:13:24
 */
@Slf4j
@Service
public class PamsBusinessSystemInfoServiceImpl extends ServiceImpl<PamsBusinessSystemInfoMapper, PamsBusinessSystemInfoDO> implements PamsBusinessSystemInfoService {

    @Resource
    private PamsBusinessSystemInfoMapper pamsBusinessSystemInfoMapper;

    @Override
    public Boolean saveInfo(PamsBusinessSystemInfoSaveBO saveBO){
        PamsBusinessSystemInfoDO entity = new PamsBusinessSystemInfoDO();
        BeanUtils.copyProperties(saveBO, entity);
        return SqlHelper.retBool(pamsBusinessSystemInfoMapper.insert(entity));
    }

    @Override
    public Boolean delInfo(Long id){
        return SqlHelper.retBool(pamsBusinessSystemInfoMapper.deleteById(id));
    }

    @Override
    public Boolean updateInfo(PamsBusinessSystemInfoUpdateBO updateBO){
        PamsBusinessSystemInfoDO entity = new PamsBusinessSystemInfoDO();
        BeanUtils.copyProperties(updateBO, entity);
        return SqlHelper.retBool(pamsBusinessSystemInfoMapper.updateById(entity));
    }

    @Override
    public PamsBusinessSystemInfoDetailVO getInfo(Long id){
        PamsBusinessSystemInfoDO entity = pamsBusinessSystemInfoMapper.selectById(id);
        return PamsBusinessSystemInfoDetailVO.of(entity);
    }

    @Override
    public IPage<PamsBusinessSystemInfoPageVO> getPageInfo(PamsBusinessSystemInfoQueryParamsBO queryParamsBO){
        return pamsBusinessSystemInfoMapper.selectPage(queryParamsBO.pageInfo(), queryParamsBO.queryWrapper()).convert(PamsBusinessSystemInfoPageVO::of);
    }

}
