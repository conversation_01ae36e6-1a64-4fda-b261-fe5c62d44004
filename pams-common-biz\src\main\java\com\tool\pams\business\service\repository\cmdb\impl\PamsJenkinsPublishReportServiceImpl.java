package com.tool.pams.business.service.repository.cmdb.impl;

import com.tool.pams.repository.domain.cmdb.bo.PamsJenkinsPublishReportSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsJenkinsPublishReportUpdateBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsJenkinsPublishReportQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.vo.PamsJenkinsPublishReportDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsJenkinsPublishReportPageVO;
import com.tool.pams.repository.domain.cmdb.db.PamsJenkinsPublishReportDO;
import com.tool.pams.repository.mapper.cmdb.PamsJenkinsPublishReportMapper;
import com.tool.pams.business.service.repository.cmdb.PamsJenkinsPublishReportService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
/**
 * <p>
 * 发布报告表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 15:12:06
 */
@Slf4j
@Service
public class PamsJenkinsPublishReportServiceImpl extends ServiceImpl<PamsJenkinsPublishReportMapper, PamsJenkinsPublishReportDO> implements PamsJenkinsPublishReportService {

    @Resource
    private PamsJenkinsPublishReportMapper pamsJenkinsPublishReportMapper;

    @Override
    public Boolean saveInfo(PamsJenkinsPublishReportSaveBO saveBO){
        PamsJenkinsPublishReportDO entity = new PamsJenkinsPublishReportDO();
        BeanUtils.copyProperties(saveBO, entity);
        return SqlHelper.retBool(pamsJenkinsPublishReportMapper.insert(entity));
    }

    @Override
    public Boolean delInfo(Long id){
        return SqlHelper.retBool(pamsJenkinsPublishReportMapper.deleteById(id));
    }

    @Override
    public Boolean updateInfo(PamsJenkinsPublishReportUpdateBO updateBO){
        PamsJenkinsPublishReportDO entity = new PamsJenkinsPublishReportDO();
        BeanUtils.copyProperties(updateBO, entity);
        return SqlHelper.retBool(pamsJenkinsPublishReportMapper.updateById(entity));
    }

    @Override
    public PamsJenkinsPublishReportDetailVO getInfo(Long id){
        PamsJenkinsPublishReportDO entity = pamsJenkinsPublishReportMapper.selectById(id);
        return PamsJenkinsPublishReportDetailVO.of(entity);
    }

    @Override
    public IPage<PamsJenkinsPublishReportPageVO> getPageInfo(PamsJenkinsPublishReportQueryParamsBO queryParamsBO){
        return pamsJenkinsPublishReportMapper.selectPage(queryParamsBO.pageInfo(), queryParamsBO.queryWrapper()).convert(PamsJenkinsPublishReportPageVO::of);
    }

}
