package com.tool.pams.repository.domain.system.vo;

import com.tool.pams.repository.domain.system.db.SysDictKeyDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-01-08 14:20:09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SysDictKeyPageVO对象", description = "字典字典")
public class SysDictKeyPageVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "字典编码")
    private String keyName;

    @Schema(description = "字典名称")
    private String keyLabel;

    @Schema(description = "字典描述")
    private String description;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;
    public SysDictKeyPageVO(SysDictKeyDO entity){
        BeanUtils.copyProperties(entity,this);
    }
}
