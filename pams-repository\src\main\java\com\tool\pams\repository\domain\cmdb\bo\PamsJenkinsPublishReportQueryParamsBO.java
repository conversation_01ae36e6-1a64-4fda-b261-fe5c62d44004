package com.tool.pams.repository.domain.cmdb.bo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.pams.repository.domain.common.PageParamsBO;
import com.tool.pams.repository.domain.cmdb.db.PamsJenkinsPublishReportDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 发布报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 15:12:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "PamsJenkinsPublishReportQueryParamsBO对象", description = "发布报告表")
public class PamsJenkinsPublishReportQueryParamsBO extends PageParamsBO<PamsJenkinsPublishReportDO> implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "唯一ID")
    private Long id;

    @Schema(description = "应用ID")
    private Long appId;

    @Schema(description = "应用名")
    private String appName;

    @Schema(description = "子应用名")
    private String subAppName;

    @Schema(description = "构建分支")
    private String branchName;

    @Schema(description = "发布人")
    private String publisher;

    @Schema(description = "发布时间-开始")
    private LocalDateTime publishTimeStart;

    @Schema(description = "发布时间-结束")
    private LocalDateTime publishTimeEnd;

    @Schema(description = "处理人")
    private String handler;

    @Schema(description = "钉钉审批单号")
    private String ddApprovalNum;

    @Schema(description = "构建ID")
    private String buildId;

    @Schema(description = "构建内容变化")
    private String buildContentChange;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "修改时间-开始")
    private LocalDateTime updateTimeStart;

    @Schema(description = "修改时间-结束")
    private LocalDateTime updateTimeEnd;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Override
    public LambdaQueryWrapper<PamsJenkinsPublishReportDO> queryWrapper() {

        LambdaQueryWrapper<PamsJenkinsPublishReportDO> query = new LambdaQueryWrapper<>();

        query.eq(id!=null,PamsJenkinsPublishReportDO::getId,id);

        query.eq(appId!=null,PamsJenkinsPublishReportDO::getAppId,appId);

        query.eq(StringUtils.isNotBlank(appName),PamsJenkinsPublishReportDO::getAppName,appName);

        query.eq(StringUtils.isNotBlank(subAppName),PamsJenkinsPublishReportDO::getSubAppName,subAppName);

        query.eq(StringUtils.isNotBlank(branchName),PamsJenkinsPublishReportDO::getBranchName,branchName);

        query.eq(StringUtils.isNotBlank(publisher),PamsJenkinsPublishReportDO::getPublisher,publisher);

        query.ge(publishTimeStart != null, PamsJenkinsPublishReportDO::getPublishTime, publishTimeStart);

        query.le(publishTimeEnd != null, PamsJenkinsPublishReportDO::getPublishTime, publishTimeEnd);

        query.eq(StringUtils.isNotBlank(handler),PamsJenkinsPublishReportDO::getHandler,handler);

        query.eq(StringUtils.isNotBlank(ddApprovalNum),PamsJenkinsPublishReportDO::getDdApprovalNum,ddApprovalNum);

        query.eq(StringUtils.isNotBlank(buildId),PamsJenkinsPublishReportDO::getBuildId,buildId);

        query.eq(StringUtils.isNotBlank(buildContentChange),PamsJenkinsPublishReportDO::getBuildContentChange,buildContentChange);

        query.ge(createTimeStart != null, PamsJenkinsPublishReportDO::getCreateTime, createTimeStart);

        query.le(createTimeEnd != null, PamsJenkinsPublishReportDO::getCreateTime, createTimeEnd);

        query.ge(updateTimeStart != null, PamsJenkinsPublishReportDO::getUpdateTime, updateTimeStart);

        query.le(updateTimeEnd != null, PamsJenkinsPublishReportDO::getUpdateTime, updateTimeEnd);

        query.eq(StringUtils.isNotBlank(creator),PamsJenkinsPublishReportDO::getCreator,creator);

        query.eq(StringUtils.isNotBlank(updater),PamsJenkinsPublishReportDO::getUpdater,updater);

        query.eq(deleted!=null,PamsJenkinsPublishReportDO::getDeleted,deleted);

        return query;
    }
}
