package ${voPath};

#set($list=["password"])
#foreach($pkg in ${table.importPackages})
#if(${pkg.indexOf("mybatisplus")}==-1)
import ${pkg};
#end
#end
import ${package.Entity}.${entity};
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * $!{table.comment}
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "${detailVO}对象", description = "$!{table.comment}")
public class ${detailVO} implements Serializable{
#if(${entitySerialVersionUID})

    private static final long serialVersionUID = 1L;
#end
    ## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})
    #if($list.contains($field.propertyName))
    #else

        #if("$!field.comment" != "")
    @Schema(description = "${field.comment}")
        #end
    private ${field.propertyType} ${field.propertyName};
    #end
#end

    public static ${detailVO} of(${entity} entity){
        if(entity == null){
            return null;
        }
        ${detailVO} detailVO = new ${detailVO}();
        BeanUtils.copyProperties(entity,detailVO);
        return detailVO;
    }

}
