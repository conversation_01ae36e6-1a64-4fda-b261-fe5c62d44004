server:
  port: 8888

spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      pool-name: HZEDHikariCP
      minimum-idle: 2
      maximum-pool-size: 10
      idle-timeout: 30000
      connection-timeout: 30000
      max-lifetime: 1800000
      connection-test-query: SELECT 1
    url: *********************************************************************************************************************************************************************************
    username: u_pams
    password: Puincn$cnsijf3mchnfei232
  redis:
    database: 237
    host: redis-local.qmwallet.vip
    port: 4467
    password: smu72fjs9bbshzp
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        min-idle: 1
        max-idle: 8
        max-wait: 3000ms
        shutdown-timeout: 5000ms
      cluster:
        refresh:
          adaptive: true
          period: 10000ms

# springdoc-openapi项目配置
springdoc:
  swagger-ui:
    enabled: true

hzed:
  sso:
    # todo:修改systemDomainCode
    systemDomainCode: test-manage-admin
    sso-service-url: https://sso-service.yichenxuanshiye.com
    access-denied-handle-type: json
    filter-chain-definition-map[/pubLogSql/save]: anon
