package com.tool.pams.repository.domain.cmdb.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.tool.pams.repository.domain.cmdb.db.PamsSystemAppInfoDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * 系统应用信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 11:13:24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "PamsSystemAppInfoPageVO对象", description = "系统应用信息表")
public class PamsSystemAppInfoPageVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "父级应用id")
    private Long parentAppId;

    @Schema(description = "应用编号")
    private String appNo;

    @Schema(description = "应用类别")
    private String appType;

    @Schema(description = "开发语言")
    private String developLanguage;

    @Schema(description = "账号ID")
    private String accountId;

    @Schema(description = "应用名称")
    private String appName;

    @Schema(description = "应用代号")
    private String appCode;

    @Schema(description = "开发负责人")
    private String developDirector;

    @Schema(description = "GitLab地址")
    private String gitlabAddress;

    @Schema(description = "Jenkins地址")
    private String jenkinsAddress;

    @Schema(description = "部署端口")
    private Integer deployPort;

    @Schema(description = "部署路径")
    private String deployPath;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Schema(description = "说明")
    private String remark;

    public static PamsSystemAppInfoPageVO of(PamsSystemAppInfoDO entity){
        if(entity == null){
            return null;
        }
        PamsSystemAppInfoPageVO pageVO = new PamsSystemAppInfoPageVO();
        BeanUtils.copyProperties(entity,pageVO);
        return pageVO;
    }

}
