<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.pams.repository.mapper.cmdb.PamsBusinessSystemAppRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemAppRelationDO">
        <id column="id" property="id" />
        <result column="business_system_id" property="businessSystemId" />
        <result column="app_id" property="appId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, business_system_id, app_id, create_time, update_time, creator, updater, deleted
    </sql>

    <select id="syncPamsBusinessSystemAppRelation" resultType="com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemAppRelationDO">
        SELECT
            id,
            create_time,
            update_time,
            is_delete as deleted,
            business_system_id,
            app_id
        FROM
            t_cmdb_business_system_app_relation
        order by create_time
    </select>
    
    <insert id="insertOrUpdateBatch">
        INSERT INTO t_pams_business_system_app_relation (
            id, business_system_id, app_id, create_time, update_time, creator, updater, deleted
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.id}, #{record.businessSystemId}, #{record.appId}, #{record.createTime},
                #{record.updateTime}, #{record.creator}, #{record.updater}, #{record.deleted}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            business_system_id = VALUES(business_system_id),
            app_id = VALUES(app_id),
            update_time = VALUES(update_time),
            updater = VALUES(updater),
            deleted = VALUES(deleted)
    </insert>

</mapper>
