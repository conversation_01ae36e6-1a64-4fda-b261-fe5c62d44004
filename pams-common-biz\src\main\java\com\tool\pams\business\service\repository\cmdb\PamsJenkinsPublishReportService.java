package com.tool.pams.business.service.repository.cmdb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.pams.repository.domain.cmdb.db.PamsJenkinsPublishReportDO;
import com.tool.pams.repository.domain.cmdb.bo.PamsJenkinsPublishReportSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsJenkinsPublishReportUpdateBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsJenkinsPublishReportQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.vo.PamsJenkinsPublishReportDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsJenkinsPublishReportPageVO;

/**
 * <p>
 * 发布报告表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 15:12:06
 */
public interface PamsJenkinsPublishReportService extends IService<PamsJenkinsPublishReportDO> {

    /**
     * 添加信息
     *
     * @param saveBO
     * @return
     */
    Boolean saveInfo(PamsJenkinsPublishReportSaveBO saveBO);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delInfo(Long id);

    /**
     * 修改信息
     *
     * @param updateBO
     * @return
     */
    Boolean updateInfo(PamsJenkinsPublishReportUpdateBO updateBO);

    /**
     * 通过ID获取信息
     *
     * @param id
     * @return
     */
    PamsJenkinsPublishReportDetailVO getInfo(Long id);

    /**
     * 分页获取列表
     *
     * @param queryParamsBO
     * @return
     */
    IPage<PamsJenkinsPublishReportPageVO> getPageInfo(PamsJenkinsPublishReportQueryParamsBO queryParamsBO);

}
