package com.tool.pams.api.web.controller.test;

import com.tool.pams.common.domain.dto.UserDTO;
import com.hzed.structure.common.util.AssertUtil;
import com.hzed.structure.common.util.ThreadUtil;
import com.hzed.structure.lock.annotation.Lock;
import com.hzed.structure.lock.enums.LockType;
import com.hzed.structure.log.annotation.PrintLog;
import com.hzed.structure.tool.annotation.PubResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzed.structure.http.api.RestTemplates;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 鎖
 * @date 2021/7/23
 **/
@Slf4j
@Tag(name = "user", description = "用户")
@RestController
@RequestMapping("/lock")
@RequiredArgsConstructor
public class LockController {


    public static void main(String[] args) {
        RestTemplates.get("xx")
                .asString()
                .getBody()
                .ifPresent(System.err::println);

    }

    @PrintLog("分页")
    @PubResponse
    @GetMapping("/index")
    @Lock(keys = {"#dto.id"})
    public String index(UserDTO dto) {
        AssertUtil.notNull(dto.getId(), "ID不能为空");
        log.info("==开始==");
        ThreadUtil.sleepMs(2000);
        log.info("==结束==");
        return "==SUCCESS==";
    }

    @PubResponse
    @PrintLog(value = "测试限流")
    @Lock(lockType = LockType.RATE_COUNT, keys = {"#dto.id"}, rateValue = 3, leaseTime = 60)
    @GetMapping("/index2")
    public String index2(UserDTO dto) {
        AssertUtil.notNull(dto.getId(), "ID不能为空");
        log.info("lock 开始");
        log.info("lock 结束");
        return "==SUCCESS==";
    }
}
