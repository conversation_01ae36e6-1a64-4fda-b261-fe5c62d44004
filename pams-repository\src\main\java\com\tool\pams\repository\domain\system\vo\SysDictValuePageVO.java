package com.tool.pams.repository.domain.system.vo;

import com.tool.pams.repository.domain.system.db.SysDictValueDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 字典配置值
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08 14:20:27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SysDictValuePageVO对象", description = "字典配置值")
public class SysDictValuePageVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "字典id")
    private Long keyId;

    @Schema(description = "字典编码")
    private String keyName;

    @Schema(description = "字典值")
    private String value;

    @Schema(description = "字典项")
    private String label;

    @Schema(description = "说明")
    private String description;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;
    public SysDictValuePageVO(SysDictValueDO entity){
        BeanUtils.copyProperties(entity,this);
    }
}
