package com.tool.pams.repository.domain.cmdb.db;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 业务系统与应用关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 15:56:15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_pams_business_system_app_relation")
@Schema(name = "PamsBusinessSystemAppRelationDO对象", description = "业务系统与应用关联表")
public class PamsBusinessSystemAppRelationDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "唯一ID")
    @TableId("`id`")
    private Long id;

    @Schema(description = "业务系统id")
    @TableField("`business_system_id`")
    private Long businessSystemId;

    @Schema(description = "应用id")
    @TableField("`app_id`")
    private Long appId;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;


}
