package com.tool.pams.api;

import cn.hutool.core.net.NetUtil;
import com.hzed.structure.lock.annotation.EnableLock;
import com.hzed.structure.log.annotation.EnableLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import com.tool.pams.common.constant.CommonConstant;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.hzed.structure.secure.annotation.EnableSecure;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/7/22
 **/

@EnableLock
@EnableLog
@EnableSecure
@Slf4j
@EnableMethodCache(basePackages = CommonConstant.BASE_PACKAGE)
@SpringBootApplication(scanBasePackages = CommonConstant.BASE_PACKAGE)
public class ApiApplication {

    public static void main(String[] args) {
        System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication application = new SpringApplication(ApiApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        ConfigurableApplicationContext context = application.run(args);
        ConfigurableEnvironment env = context.getEnvironment();
        String ip = NetUtil.getIpByHost(NetUtil.getLocalHostName());
        String name = env.getProperty("spring.application.name");
        String port = env.getProperty("server.port");
        String path = env.getProperty("server.servlet.context-path");
        String active = env.getProperty("spring.profiles.active");
        log.info("\n--------------------------------------------------------------------------\n\t" +
                "Application " + name + " is running, Active " + active + ".\n\t" +
                "Local: \t\thttp://localhost:" + port + path + "/\n\t" +
                "External: \thttp://" + ip + ":" + port + path + "/\n\t" +
                "Health: \thttp://" + ip + ":" + port + path + "/actuator/health\n\t" +
                "Swagger: \thttp://" + ip + ":" + port + path + "/doc.html\n" +
                "--------------------------------------------------------------------------");
    }
}
