package ${voPath};

#set($list=["password"])
#foreach($pkg in ${table.importPackages})
#if(${pkg.indexOf("mybatisplus")}==-1)
import ${pkg};
#end
#end
import ${package.Entity}.${entity};
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * $!{table.comment}
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "${pageVO}对象", description = "$!{table.comment}")
public class ${pageVO} implements Serializable{
#if(${entitySerialVersionUID})

    private static final long serialVersionUID = 1L;
#end
## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})
    #if($list.contains($field.propertyName))
    #else

        #if("$!field.comment" != "")
    @Schema(description = "${field.comment}")
        #end
    private ${field.propertyType} ${field.propertyName};
    #end
#end

    public static ${pageVO} of(${entity} entity){
        if(entity == null){
            return null;
        }
        ${pageVO} pageVO = new ${pageVO}();
        BeanUtils.copyProperties(entity,pageVO);
        return pageVO;
    }

}
