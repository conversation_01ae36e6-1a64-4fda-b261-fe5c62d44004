package com.tool.pams.business.service.repository.cmdb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.pams.repository.domain.cmdb.db.PamsAppSummaryInfoDO;
import com.tool.pams.repository.domain.cmdb.bo.PamsAppSummaryInfoSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsAppSummaryInfoUpdateBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsAppSummaryInfoQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.vo.PamsAppSummaryInfoDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsAppSummaryInfoPageVO;

import java.util.List;

/**
 * <p>
 * 应用信息汇总表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
public interface PamsAppSummaryInfoService extends IService<PamsAppSummaryInfoDO> {



    /**
     * 修改信息
     *
     * @param updateBO
     * @return
     */
    Boolean updateInfo(PamsAppSummaryInfoUpdateBO updateBO);

    /**
     * 通过ID获取信息（包含关联信息）
     *
     * @param id
     * @return
     */
    PamsAppSummaryInfoDetailVO getInfo(Long id);

    /**
     * 分页获取列表（包含关联信息）
     *
     * @param queryParamsBO
     * @return
     */
    IPage<PamsAppSummaryInfoPageVO> getPageInfo(PamsAppSummaryInfoQueryParamsBO queryParamsBO);



    /**
     * 批量保存或更新应用汇总信息
     *
     * @param summaryInfoList 应用汇总信息列表
     * @return 是否成功
     */
    Boolean batchSaveOrUpdate(List<PamsAppSummaryInfoDO> summaryInfoList);


}
