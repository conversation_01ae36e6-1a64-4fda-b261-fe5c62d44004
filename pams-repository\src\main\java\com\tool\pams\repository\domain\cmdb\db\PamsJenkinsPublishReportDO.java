package com.tool.pams.repository.domain.cmdb.db;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 发布报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 15:12:06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_pams_jenkins_publish_report")
@Schema(name = "PamsJenkinsPublishReportDO对象", description = "发布报告表")
public class PamsJenkinsPublishReportDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "唯一ID")
    @TableId(value = "`id`")
    private Long id;

    @Schema(description = "应用ID")
    @TableField("`app_id`")
    private Long appId;

    @Schema(description = "应用名")
    @TableField("`app_name`")
    private String appName;

    @Schema(description = "子应用名")
    @TableField("`sub_app_name`")
    private String subAppName;

    @Schema(description = "构建分支")
    @TableField("`branch_name`")
    private String branchName;

    @Schema(description = "发布人")
    @TableField("`publisher`")
    private String publisher;

    @Schema(description = "发布时间")
    @TableField("`publish_time`")
    private LocalDateTime publishTime;

    @Schema(description = "处理人")
    @TableField("`handler`")
    private String handler;

    @Schema(description = "钉钉审批单号")
    @TableField("`dd_approval_num`")
    private String ddApprovalNum;

    @Schema(description = "构建ID")
    @TableField("`build_id`")
    private String buildId;

    @Schema(description = "构建内容变化")
    @TableField("`build_content_change`")
    private String buildContentChange;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;


}
