package com.tool.pams.business.service.repository.cmdb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemAppRelationDO;
import com.tool.pams.repository.domain.cmdb.bo.PamsBusinessSystemAppRelationSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsBusinessSystemAppRelationUpdateBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsBusinessSystemAppRelationQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.vo.PamsBusinessSystemAppRelationDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsBusinessSystemAppRelationPageVO;

/**
 * <p>
 * 业务系统与应用关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 11:13:24
 */
public interface PamsBusinessSystemAppRelationService extends IService<PamsBusinessSystemAppRelationDO> {

    /**
     * 添加信息
     *
     * @param saveBO
     * @return
     */
    Boolean saveInfo(PamsBusinessSystemAppRelationSaveBO saveBO);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delInfo(Long id);

    /**
     * 修改信息
     *
     * @param updateBO
     * @return
     */
    Boolean updateInfo(PamsBusinessSystemAppRelationUpdateBO updateBO);

    /**
     * 通过ID获取信息
     *
     * @param id
     * @return
     */
    PamsBusinessSystemAppRelationDetailVO getInfo(Long id);

    /**
     * 分页获取列表
     *
     * @param queryParamsBO
     * @return
     */
    IPage<PamsBusinessSystemAppRelationPageVO> getPageInfo(PamsBusinessSystemAppRelationQueryParamsBO queryParamsBO);

}
