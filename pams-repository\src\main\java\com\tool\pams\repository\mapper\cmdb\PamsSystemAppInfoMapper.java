package com.tool.pams.repository.mapper.cmdb;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.tool.pams.repository.domain.cmdb.db.PamsSystemAppInfoDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 系统应用信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 11:13:24
 */
@Mapper
public interface PamsSystemAppInfoMapper extends BaseMapper<PamsSystemAppInfoDO> {

    /**
     * 同步应用
     *
     * @return
     */
    @DS("cmdb")
    List<PamsSystemAppInfoDO> syncSystemAppInfo();

    /**
     * 批量插入或更新系统应用信息（使用INSERT ... ON DUPLICATE KEY UPDATE）
     *
     * @param records 系统应用信息列表
     * @return 受影响的行数
     */
    int insertOrUpdateBatch(@Param("records") List<PamsSystemAppInfoDO> records);

}
