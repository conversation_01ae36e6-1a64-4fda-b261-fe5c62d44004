package com.tool.pams.business.service.repository.cmdb.impl;

import com.tool.pams.repository.domain.cmdb.bo.PamsAppSummaryInfoSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsAppSummaryInfoUpdateBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsAppSummaryInfoQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.vo.PamsAppSummaryInfoDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsAppSummaryInfoPageVO;
import com.tool.pams.repository.domain.cmdb.db.PamsAppSummaryInfoDO;
import com.tool.pams.repository.mapper.cmdb.PamsAppSummaryInfoMapper;
import com.tool.pams.business.service.repository.cmdb.PamsAppSummaryInfoService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * <p>
 * 应用信息汇总表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Service
public class PamsAppSummaryInfoServiceImpl extends ServiceImpl<PamsAppSummaryInfoMapper, PamsAppSummaryInfoDO> implements PamsAppSummaryInfoService {

    @Resource
    private PamsAppSummaryInfoMapper pamsAppSummaryInfoMapper;



    @Override
    public Boolean updateInfo(PamsAppSummaryInfoUpdateBO updateBO){
        PamsAppSummaryInfoDO entity = new PamsAppSummaryInfoDO();
        BeanUtils.copyProperties(updateBO, entity);
        return SqlHelper.retBool(pamsAppSummaryInfoMapper.updateById(entity));
    }

    @Override
    public PamsAppSummaryInfoDetailVO getInfo(Long id){
        return pamsAppSummaryInfoMapper.selectDetailById(id);
    }

    @Override
    public IPage<PamsAppSummaryInfoPageVO> getPageInfo(PamsAppSummaryInfoQueryParamsBO queryParamsBO){
        Page<PamsAppSummaryInfoPageVO> page = new Page<>(queryParamsBO.getPageNum(), queryParamsBO.getPageSize());
        return pamsAppSummaryInfoMapper.selectPageWithRelation(page, queryParamsBO);
    }



    @Override
    public Boolean batchSaveOrUpdate(List<PamsAppSummaryInfoDO> summaryInfoList) {
        if (summaryInfoList == null || summaryInfoList.isEmpty()) {
            return true;
        }
        return SqlHelper.retBool(pamsAppSummaryInfoMapper.insertOrUpdateBatch(summaryInfoList));
    }


}
