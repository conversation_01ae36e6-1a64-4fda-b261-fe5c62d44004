package com.tool.pams.api.web.controller.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzed.structure.log.annotation.PrintLog;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.pams.business.service.repository.system.SysDictKeyService;
import com.tool.pams.repository.domain.system.bo.SysDictKeyQueryParamsBO;
import com.tool.pams.repository.domain.system.bo.SysDictKeySaveBO;
import com.tool.pams.repository.domain.system.bo.SysDictKeyUpdateBO;
import com.tool.pams.repository.domain.system.vo.SysDictKeyDetailVO;
import com.tool.pams.repository.domain.system.vo.SysDictKeyPageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 字典 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Tag(name = "字典")
@RestController
@RequestMapping("/sysDictKey")
public class SysDictKeyController {

    @Autowired
    SysDictKeyService dictKeyService;

    @ApiResponse
    @Operation(summary = "字典分页列表查询")
    @PrintLog("字典分页列表查询")
    @GetMapping("/page")
    public IPage<SysDictKeyPageVO> page(@ParameterObject @Valid SysDictKeyQueryParamsBO bo){
        return dictKeyService.selectPage(bo);
    }

    @ApiResponse
    @Operation(summary = "字典详情查询")
    @PrintLog("字典详情查询")
    @GetMapping("/info/{id}")
    public SysDictKeyDetailVO info(@PathVariable("id") Long id){
        return dictKeyService.getInfo(id);
    }

    @ApiResponse
    @Operation(summary = "新增字典")
    @PrintLog("新增字典")
    @PostMapping("/save")
    public Boolean save(@RequestBody @Valid SysDictKeySaveBO dict){
        return dictKeyService.saveDictKey(dict);
    }

    @ApiResponse
    @Operation(summary = "修改字典")
    @PrintLog("修改字典")
    @PostMapping("/update")
    public Boolean update(@RequestBody @Valid SysDictKeyUpdateBO dictKeyUpdateBO) {
        return dictKeyService.updateDictKey(dictKeyUpdateBO);
    }

    @ApiResponse
    @Operation(summary = "删除字典")
    @PrintLog("删除字典")
    @PostMapping("/del/{dictId}")
    public Boolean del(@PathVariable("dictId") Long dictId) {
        return dictKeyService.deleteDictTypeByIds(dictId);
    }

}