package com.tool.pams.repository.domain.cmdb.bo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.pams.repository.domain.common.PageParamsBO;
import com.tool.pams.repository.domain.cmdb.db.PamsSystemAppInfoDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 系统应用信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 11:13:24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "PamsSystemAppInfoQueryParamsBO对象", description = "系统应用信息表")
public class PamsSystemAppInfoQueryParamsBO extends PageParamsBO<PamsSystemAppInfoDO> implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "父级应用id")
    private Long parentAppId;

    @Schema(description = "应用编号")
    private String appNo;

    @Schema(description = "应用类别")
    private String appType;

    @Schema(description = "开发语言")
    private String developLanguage;

    @Schema(description = "账号ID")
    private String accountId;

    @Schema(description = "应用名称")
    private String appName;

    @Schema(description = "应用代号")
    private String appCode;

    @Schema(description = "开发负责人")
    private String developDirector;

    @Schema(description = "GitLab地址")
    private String gitlabAddress;

    @Schema(description = "Jenkins地址")
    private String jenkinsAddress;

    @Schema(description = "部署端口")
    private Integer deployPort;

    @Schema(description = "部署路径")
    private String deployPath;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "修改时间-开始")
    private LocalDateTime updateTimeStart;

    @Schema(description = "修改时间-结束")
    private LocalDateTime updateTimeEnd;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Schema(description = "说明")
    private String remark;

    @Override
    public LambdaQueryWrapper<PamsSystemAppInfoDO> queryWrapper() {

        LambdaQueryWrapper<PamsSystemAppInfoDO> query = new LambdaQueryWrapper<>();

        query.eq(id!=null,PamsSystemAppInfoDO::getId,id);

        query.eq(parentAppId!=null,PamsSystemAppInfoDO::getParentAppId,parentAppId);

        query.eq(StringUtils.isNotBlank(appNo),PamsSystemAppInfoDO::getAppNo,appNo);

        query.eq(StringUtils.isNotBlank(appType),PamsSystemAppInfoDO::getAppType,appType);

        query.eq(StringUtils.isNotBlank(developLanguage),PamsSystemAppInfoDO::getDevelopLanguage,developLanguage);

        query.eq(StringUtils.isNotBlank(accountId),PamsSystemAppInfoDO::getAccountId,accountId);

        query.eq(StringUtils.isNotBlank(appName),PamsSystemAppInfoDO::getAppName,appName);

        query.eq(StringUtils.isNotBlank(appCode),PamsSystemAppInfoDO::getAppCode,appCode);

        query.eq(StringUtils.isNotBlank(developDirector),PamsSystemAppInfoDO::getDevelopDirector,developDirector);

        query.eq(StringUtils.isNotBlank(gitlabAddress),PamsSystemAppInfoDO::getGitlabAddress,gitlabAddress);

        query.eq(StringUtils.isNotBlank(jenkinsAddress),PamsSystemAppInfoDO::getJenkinsAddress,jenkinsAddress);

        query.eq(deployPort!=null,PamsSystemAppInfoDO::getDeployPort,deployPort);

        query.eq(StringUtils.isNotBlank(deployPath),PamsSystemAppInfoDO::getDeployPath,deployPath);

        query.ge(createTimeStart != null, PamsSystemAppInfoDO::getCreateTime, createTimeStart);

        query.le(createTimeEnd != null, PamsSystemAppInfoDO::getCreateTime, createTimeEnd);

        query.ge(updateTimeStart != null, PamsSystemAppInfoDO::getUpdateTime, updateTimeStart);

        query.le(updateTimeEnd != null, PamsSystemAppInfoDO::getUpdateTime, updateTimeEnd);

        query.eq(StringUtils.isNotBlank(creator),PamsSystemAppInfoDO::getCreator,creator);

        query.eq(StringUtils.isNotBlank(updater),PamsSystemAppInfoDO::getUpdater,updater);

        query.eq(deleted!=null,PamsSystemAppInfoDO::getDeleted,deleted);

        query.eq(StringUtils.isNotBlank(remark),PamsSystemAppInfoDO::getRemark,remark);

        return query;
    }
}
