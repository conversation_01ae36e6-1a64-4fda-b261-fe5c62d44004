server:
  port: 8001
  shutdown: graceful
  servlet:
    context-path: /pams-job
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: pams-job
  profiles:
    active: dev
  lifecycle:
    timeout-per-shutdown-phase: 85s
    main:
      allow-bean-definition-overriding: true
    jackson:
      date-format: yyyy-MM-dd HH:mm:ss
      time-zone: GMT+8
    messages:
      basename: i18n/messages,i18n/PubResultCode

  # MyBatisPlus
  mybatis-plus:
    mapper-locations: classpath*:/mapper/**/*Mapper.xml
    global-config:
      banner: true
      db-config:
        id-type: ASSIGN_ID

  # 组件配置
  pub:
    tool:
      error-mess-handler-enabled: true
      long-to-string-enabled: true
      java-time-module-enabled: true
      http-error-json-handler-enabled: true

  # prometheus
  management:
    metrics:
      tags:
        application: ${spring.application.name}
      mongo:
        connectionpool:
          enabled: false
        command:
          enabled: false
    endpoints:
      web:
        exposure:
          include: health,metrics,prometheus
    endpoint:
      health:
        show-details: always
