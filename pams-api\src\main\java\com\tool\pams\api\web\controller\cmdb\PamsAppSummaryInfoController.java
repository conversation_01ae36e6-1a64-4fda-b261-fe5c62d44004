package com.tool.pams.api.web.controller.cmdb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import com.hzed.structure.log.annotation.PrintLog;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.pams.business.service.repository.cmdb.PamsAppSummaryInfoService;
import com.tool.pams.repository.domain.cmdb.bo.PamsAppSummaryInfoSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsAppSummaryInfoUpdateBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsAppSummaryInfoQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.vo.PamsAppSummaryInfoDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsAppSummaryInfoPageVO;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 应用信息汇总表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Tag(name = "应用信息汇总表")
@RestController
@RequestMapping("/pamsAppSummaryInfo")
public class PamsAppSummaryInfoController {

    @Resource
    private PamsAppSummaryInfoService pamsAppSummaryInfoService;

    @ApiResponse
    @Operation(summary = "根据id查询应用信息汇总表")
    @PrintLog("根据id查询应用信息汇总表")
    @GetMapping("/info/{id}")
    public PamsAppSummaryInfoDetailVO info(@PathVariable("id") Long id) {
        return pamsAppSummaryInfoService.getInfo(id);
    }



    @ApiResponse
    @Operation(summary = "分页查询应用信息汇总表")
    @PrintLog("分页查询应用信息汇总表")
    @GetMapping("/page")
    public IPage<PamsAppSummaryInfoPageVO> page(@Valid @ParameterObject PamsAppSummaryInfoQueryParamsBO queryParamsBO) {
        return pamsAppSummaryInfoService.getPageInfo(queryParamsBO);
    }



    @ApiResponse
    @Operation(summary = "修改应用信息汇总表")
    @PrintLog("修改应用信息汇总表")
    @PostMapping("/update")
    public Boolean update(@RequestBody @Valid PamsAppSummaryInfoUpdateBO updateBO) {
        return pamsAppSummaryInfoService.updateInfo(updateBO);
    }




}
