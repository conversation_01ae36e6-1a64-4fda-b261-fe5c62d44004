<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.repository.mapper.test.DictMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.pams.repository.domain.db.Dict">
        <id column="id" property="id"/>
        <result column="dict_code" property="dictCode"/>
        <result column="dict_value" property="dictValue"/>
        <result column="dict_name" property="dictName"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="dict_desc" property="dictDesc"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , dict_code, dict_value, dict_name, is_deleted, dict_desc, create_time, update_time
    </sql>

</mapper>
