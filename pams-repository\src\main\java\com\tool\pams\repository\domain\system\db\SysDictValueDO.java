package com.tool.pams.repository.domain.system.db;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 字典配置值
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08 14:20:27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_sys_dict_value")
@Schema(name = "SysDictValueDO对象", description = "字典配置值")
public class SysDictValueDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    @TableId(value = "`id`", type = IdType.AUTO)
    private Long id;

    @Schema(description = "字典id")
    @TableField("`key_id`")
    private Long keyId;

    @Schema(description = "字典编码")
    @TableField("`key_name`")
    private String keyName;

    @Schema(description = "字典值")
    @TableField("`value`")
    private String value;

    @Schema(description = "字典项")
    @TableField("`label`")
    private String label;

    @Schema(description = "说明")
    @TableField("`description`")
    private String description;

    @Schema(description = "排序")
    @TableField("`sort`")
    private Integer sort;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;

}
