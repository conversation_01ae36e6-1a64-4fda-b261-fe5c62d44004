package com.tool.pams.business.service.repository.cmdb.impl;

import com.tool.pams.business.service.repository.cmdb.PamsBusinessSystemAppRelationService;
import com.tool.pams.repository.domain.cmdb.bo.PamsBusinessSystemAppRelationSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsBusinessSystemAppRelationUpdateBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsBusinessSystemAppRelationQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.vo.PamsBusinessSystemAppRelationDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsBusinessSystemAppRelationPageVO;
import com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemAppRelationDO;
import com.tool.pams.repository.mapper.cmdb.PamsBusinessSystemAppRelationMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
/**
 * <p>
 * 业务系统与应用关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 11:13:24
 */
@Slf4j
@Service
public class PamsBusinessSystemAppRelationServiceImpl extends ServiceImpl<PamsBusinessSystemAppRelationMapper, PamsBusinessSystemAppRelationDO> implements PamsBusinessSystemAppRelationService {

    @Resource
    private PamsBusinessSystemAppRelationMapper pamsBusinessSystemAppRelationMapper;

    @Override
    public Boolean saveInfo(PamsBusinessSystemAppRelationSaveBO saveBO){
        PamsBusinessSystemAppRelationDO entity = new PamsBusinessSystemAppRelationDO();
        BeanUtils.copyProperties(saveBO, entity);
        return SqlHelper.retBool(pamsBusinessSystemAppRelationMapper.insert(entity));
    }

    @Override
    public Boolean delInfo(Long id){
        return SqlHelper.retBool(pamsBusinessSystemAppRelationMapper.deleteById(id));
    }

    @Override
    public Boolean updateInfo(PamsBusinessSystemAppRelationUpdateBO updateBO){
        PamsBusinessSystemAppRelationDO entity = new PamsBusinessSystemAppRelationDO();
        BeanUtils.copyProperties(updateBO, entity);
        return SqlHelper.retBool(pamsBusinessSystemAppRelationMapper.updateById(entity));
    }

    @Override
    public PamsBusinessSystemAppRelationDetailVO getInfo(Long id){
        PamsBusinessSystemAppRelationDO entity = pamsBusinessSystemAppRelationMapper.selectById(id);
        return PamsBusinessSystemAppRelationDetailVO.of(entity);
    }

    @Override
    public IPage<PamsBusinessSystemAppRelationPageVO> getPageInfo(PamsBusinessSystemAppRelationQueryParamsBO queryParamsBO){
        return pamsBusinessSystemAppRelationMapper.selectPage(queryParamsBO.pageInfo(), queryParamsBO.queryWrapper()).convert(PamsBusinessSystemAppRelationPageVO::of);
    }

}
