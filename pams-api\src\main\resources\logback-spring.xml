<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="true" scanPeriod="1 seconds">
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr"
                    converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

    <springProperty scope="context" name="serverPort" source="server.port" defaultValue="UnknownPort"/>
    <property name="logback.logDir" value="/www/wwwlogs/pams-api"/>
    <property name="logback.appName" value="pams-api"/>
    <property name="console.log.pattern"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%clr(%level)] [%X{PtxId}] [%X{Pub-hostName}] [%X{Pub-IpStr}] [%X{Pub-TraceId},%X{Pub-TxId}] [%X{Pub-UriPath}] [%X{Pub-ModuleName}] [%t] [%clr(%logger{1}:%L){cyan}]  - %msg%n"/>
    <property name="log.pattern"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level] [%X{PtxId}] [%X{Pub-hostName}] [%X{Pub-IpStr}] [%X{Pub-TraceId},%X{Pub-TxId}] [%X{Pub-UriPath}] [%X{Pub-ModuleName}] [%t] [%c{0}:%L] - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${console.log.pattern}</pattern>
        </encoder>
    </appender>


    <!-- 滚动日志 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logback.logDir}/allLog/${logback.appName}.${serverPort}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${logback.logDir}/allLog/${logback.appName}.%d{yyyy-MM-dd}.${serverPort}.log
            </FileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="FILE"/>
        <springProfile name="!prod">
            <appender-ref ref="CONSOLE"/>
        </springProfile>
    </root>

    <springProfile name="!prod">
        <logger name="org.springframework" level="warn"/>
        <logger name="org.apache.http" level="warn"/>
        <logger name="org.springframework.web" level="error"/>
        <logger name="io.lettuce.core" level="warn"/>
        <logger name="org.springframework.web" level="error"/>
        <logger name="io.lettuce.core" level="warn"/>
        <logger name="com.xxl.job.core.thread" level="warn"/>
        <logger name="com.tool.pams.repository.mapper" level="debug"/>
        <logger name="com.hzed.structure" level="debug"/>
        <logger name="com.baomidou.mybatisplus" level="debug"/>
    </springProfile>

    <springProfile name="prod">
        <logger name="org.springframework" level="warn"/>
        <logger name="org.apache.http" level="warn"/>
        <logger name="org.springframework.web" level="error"/>
        <logger name="io.lettuce.core" level="warn"/>
        <logger name="com.jcraft" level="warn"/>
        <logger name="com.tool.pams.repository.mapper" level="info"/>
        <logger name="com.hzed.structure" level="info"/>
    </springProfile>

</configuration>