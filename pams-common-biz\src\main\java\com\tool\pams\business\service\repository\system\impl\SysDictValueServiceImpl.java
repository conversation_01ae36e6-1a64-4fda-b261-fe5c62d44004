package com.tool.pams.business.service.repository.system.impl;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.hzed.structure.common.exception.ServiceException;
import com.tool.pams.business.service.repository.system.SysDictKeyService;
import com.tool.pams.business.service.repository.system.SysDictValueService;
import com.tool.pams.repository.domain.system.bo.SysDictValueQueryParamsBO;
import com.tool.pams.repository.domain.system.bo.SysDictValueSaveBO;
import com.tool.pams.repository.domain.system.bo.SysDictValueUpdateBO;
import com.tool.pams.repository.domain.system.db.SysDictKeyDO;
import com.tool.pams.repository.domain.system.db.SysDictValueDO;
import com.tool.pams.repository.domain.system.vo.SysDictValuePageVO;
import com.tool.pams.repository.mapper.system.SysDictValueMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 字典配置值 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@Service
public class SysDictValueServiceImpl extends ServiceImpl<SysDictValueMapper, SysDictValueDO> implements SysDictValueService {

    @Resource
    SysDictKeyService dictKeyService;

    @Override
    public IPage<SysDictValuePageVO> selectPage(SysDictValueQueryParamsBO sysDictValueQueryParamsBO) {
       return baseMapper.selectPage(sysDictValueQueryParamsBO.pageInfo(), sysDictValueQueryParamsBO.queryWrapper())
               .convert(SysDictValuePageVO::new);
    }

    @Override
    public Boolean saveInfo(SysDictValueSaveBO saveBO){
        SysDictValueDO entity = new SysDictValueDO();
        BeanUtils.copyProperties(saveBO, entity);
        if (checkLabelUnique(entity)) {
            throw new ServiceException("字典项已存在");
        }
        if(checkValueUnique(entity)){
            throw new ServiceException("字典值已存在");
        }
        SysDictKeyDO dictKey = dictKeyService.getById(entity.getKeyId());
        entity.setKeyName(dictKey.getKeyName());
        return SqlHelper.retBool(baseMapper.insert(entity));
    }

    @Override
    public Boolean delInfo(Long id){
        return SqlHelper.retBool(baseMapper.deleteById(id));
    }

    @Override
    public Boolean updateInfo(SysDictValueUpdateBO updateBO){
        SysDictValueDO entity = new SysDictValueDO();
        BeanUtils.copyProperties(updateBO, entity);
        if (checkLabelUnique(entity)) {
            throw new ServiceException("字典项已存在");
        }
        if(checkValueUnique(entity)){
            throw new ServiceException("字典值已存在");
        }
        SysDictKeyDO dictKey = dictKeyService.getById(entity.getKeyId());
        entity.setKeyName(dictKey.getKeyName());
        return SqlHelper.retBool(baseMapper.updateById(entity));
    }

    @Override
    public List<SysDictValueDO> listByKeyName(String keyName) {
        List<SysDictValueDO> sysDictValues = baseMapper.selectList(new LambdaQueryWrapper<SysDictValueDO>()
                .eq(SysDictValueDO::getKeyName, keyName)
                .orderByAsc(SysDictValueDO::getSort));
        return sysDictValues;
    }

    /**
     * 判断字典配置值是否唯一
     * @param dictValue
     * @return
     */
    private Boolean checkValueUnique(SysDictValueDO dictValue){
        return baseMapper.exists(new LambdaQueryWrapper<SysDictValueDO>()
                .ne(ObjectUtil.isNotNull(dictValue.getId()),SysDictValueDO::getId, dictValue.getId())
                .eq(SysDictValueDO::getKeyId, dictValue.getKeyId())
                .eq(SysDictValueDO::getValue, dictValue.getValue()));
    }

    private Boolean checkLabelUnique(SysDictValueDO dictValue){
        return baseMapper.exists(new LambdaQueryWrapper<SysDictValueDO>()
                .ne(ObjectUtil.isNotNull(dictValue.getId()),SysDictValueDO::getId, dictValue.getId())
                .eq(SysDictValueDO::getKeyId, dictValue.getKeyId())
                .eq(SysDictValueDO::getLabel, dictValue.getLabel()));
    }

}
