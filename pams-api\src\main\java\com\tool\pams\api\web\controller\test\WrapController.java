package com.tool.pams.api.web.controller.test;

import com.tool.pams.common.domain.dto.UserDTO;
import com.hzed.structure.log.annotation.PrintLog;
import com.hzed.structure.notice.util.WarnTemplateUtil;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.hzed.structure.tool.annotation.CommonResponse;
import com.hzed.structure.tool.annotation.PubResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 可以选择三个响应体 结构,如果选择的是CommonResponse,需要自己处理全局异常,可以参考 MessApiExceptionHandler
 * @date 2021/7/23
 **/
@Slf4j
@Tag(name = "user", description = "用户")
@RestController
@RequestMapping("/wrap")
@RequiredArgsConstructor
public class WrapController {
    /**
     * 响应体对应 ：
     *
     * @return
     * @see com.hzed.structure.tool.api.ApiResponse
     */
    @PrintLog("测试index")
    @ApiResponse
    @GetMapping("/index")
    public UserDTO apiResponse() {
        log.info("测试Index");
        UserDTO user = new UserDTO();
        user.setId(88888L);
        user.setTime(LocalDateTime.now());

        //发送钉钉消息
        WarnTemplateUtil.sendNormalNotice("测试3", user);
        return user;
    }

    /**
     * 响应体对应 ：
     *
     * @return
     * @see com.hzed.structure.tool.api.PubResponse
     */
    @PrintLog("测试index2")
    @PubResponse
    @GetMapping("/index2")
    public UserDTO pubResponse() {
        log.info("测试Index2");
        UserDTO user = new UserDTO();
        user.setId(88888L);
        user.setTime(LocalDateTime.now());
        return user;
    }

    /**
     * 响应体对应 ：
     *
     * @return
     * @see com.hzed.structure.tool.api.CommonResponse
     */
    @PrintLog("测试index3")
    @CommonResponse
    @GetMapping("/index3")
    public UserDTO commonResponse() {
        log.info("测试Index3");
        UserDTO user = new UserDTO();
        user.setId(88888L);
        user.setTime(LocalDateTime.now());
        return user;
    }


}
