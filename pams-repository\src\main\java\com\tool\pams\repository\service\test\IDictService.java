package com.tool.pams.repository.service.test;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.pams.repository.domain.db.Dict;

import java.util.List;

/**
 * <p>
 * 数据字典表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-22
 */
public interface IDictService extends IService<Dict> {

    /**
     * 通过code获取
     *
     * @param code
     * @return
     */
    List<Dict> queryByCode(String code);

    /**
     * 分页
     *
     * @param pageDTO
     * @param code
     * @return
     */
    Page<Dict> getPage(Page<Dict> pageDTO, String code);
}
