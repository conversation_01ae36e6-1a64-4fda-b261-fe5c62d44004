package com.tool.pams.repository.domain.cmdb.bo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.pams.repository.domain.common.PageParamsBO;
import com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemInfoDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 新业务系统资源信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 11:13:24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "PamsBusinessSystemInfoQueryParamsBO对象", description = "新业务系统资源信息表")
public class PamsBusinessSystemInfoQueryParamsBO extends PageParamsBO<PamsBusinessSystemInfoDO> implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "系统编码")
    private String systemNo;

    @Schema(description = "系统名称")
    private String systemName;

    @Schema(description = "系统代号")
    private String systemCode;

    @Schema(description = "系统类别")
    private String systemType;

    @Schema(description = "账号ID")
    private String accountId;

    @Schema(description = "负责人")
    private String director;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "修改时间-开始")
    private LocalDateTime updateTimeStart;

    @Schema(description = "修改时间-结束")
    private LocalDateTime updateTimeEnd;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Override
    public LambdaQueryWrapper<PamsBusinessSystemInfoDO> queryWrapper() {

        LambdaQueryWrapper<PamsBusinessSystemInfoDO> query = new LambdaQueryWrapper<>();

        query.eq(id!=null,PamsBusinessSystemInfoDO::getId,id);

        query.eq(StringUtils.isNotBlank(systemNo),PamsBusinessSystemInfoDO::getSystemNo,systemNo);

        query.eq(StringUtils.isNotBlank(systemName),PamsBusinessSystemInfoDO::getSystemName,systemName);

        query.eq(StringUtils.isNotBlank(systemCode),PamsBusinessSystemInfoDO::getSystemCode,systemCode);

        query.eq(StringUtils.isNotBlank(systemType),PamsBusinessSystemInfoDO::getSystemType,systemType);

        query.eq(StringUtils.isNotBlank(accountId),PamsBusinessSystemInfoDO::getAccountId,accountId);

        query.eq(StringUtils.isNotBlank(director),PamsBusinessSystemInfoDO::getDirector,director);

        query.ge(createTimeStart != null, PamsBusinessSystemInfoDO::getCreateTime, createTimeStart);

        query.le(createTimeEnd != null, PamsBusinessSystemInfoDO::getCreateTime, createTimeEnd);

        query.ge(updateTimeStart != null, PamsBusinessSystemInfoDO::getUpdateTime, updateTimeStart);

        query.le(updateTimeEnd != null, PamsBusinessSystemInfoDO::getUpdateTime, updateTimeEnd);

        query.eq(StringUtils.isNotBlank(creator),PamsBusinessSystemInfoDO::getCreator,creator);

        query.eq(StringUtils.isNotBlank(updater),PamsBusinessSystemInfoDO::getUpdater,updater);

        query.eq(deleted!=null,PamsBusinessSystemInfoDO::getDeleted,deleted);

        return query;
    }
}
