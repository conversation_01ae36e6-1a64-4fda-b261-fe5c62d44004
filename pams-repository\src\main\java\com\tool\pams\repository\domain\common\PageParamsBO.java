package com.tool.pams.repository.domain.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzed.structure.common.exception.ServiceException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 分页查询参数
 *
 * @param <T> DO类型
 * <AUTHOR>
 * @since 2025/1/7 9:01
 */
@Schema(description = "Mybatis分页查询参数")
@Data
public class PageParamsBO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分页大小")
    private Integer pageSize;


    @Schema(description = "当前页数")
    private Integer pageNum;


    @Schema(description = "排序字段（多个英文逗号隔开）")
    private String orderFields;


    @Schema(description = "排序规则：desc或asc（多个英文逗号隔开）")
    private String orderRules;

    /**
     * 当前记录起始索引 默认值
     */
    public static final int DEFAULT_PAGE_NUM = 1;

    /**
     * 每页显示记录数 默认值 默认查全部
     */
    public static final int DEFAULT_PAGE_SIZE = Integer.MAX_VALUE;

    /**
     * 仅支持字母、数字、下划线、空格、逗号、小数点（支持多个字段排序）
     */
    public static final String SQL_PATTERN = "[\\w ,.]+";

    public LambdaQueryWrapper<T> queryWrapper() {
        return new LambdaQueryWrapper<>();
    }

    public Page<T> pageInfo() {
        Integer current = ObjectUtil.defaultIfNull(getPageNum(), DEFAULT_PAGE_NUM);
        Integer size = ObjectUtil.defaultIfNull(getPageSize(), DEFAULT_PAGE_SIZE);
        if (current <= 0) {
            current = DEFAULT_PAGE_NUM;
        }
        Page<T> page = new Page<>(current, size);
        List<OrderItem> orderItems = buildOrderItem();
        if (CollUtil.isNotEmpty(orderItems)) {
            page.addOrder(orderItems);
        }
        return page;
    }

    /**
     * 构建排序
     * <p>
     * 支持的用法如下:
     * {isAsc:"asc",orderByColumn:"id"} order by id asc
     * {isAsc:"asc",orderByColumn:"id,createTime"} order by id asc,create_time asc
     * {isAsc:"desc",orderByColumn:"id,createTime"} order by id desc,create_time desc
     * {isAsc:"asc,desc",orderByColumn:"id,createTime"} order by id asc,create_time desc
     */
    private List<OrderItem> buildOrderItem() {
        if (StringUtils.isAnyBlank(orderFields, orderRules)) {
            return Collections.emptyList();
        }
        if (StringUtils.isNotBlank(orderFields) && !orderFields.matches(SQL_PATTERN)) {
            throw new ServiceException("参数不符合规范，不能进行查询");
        }
        String orderBy = com.baomidou.mybatisplus.core.toolkit.StringUtils.camelToUnderline(orderFields);


        // 兼容前端排序类型
        orderRules = StringUtils.replaceEach(orderRules, new String[]{"ascending", "descending"}, new String[]{"asc", "desc"});

        String[] orderByArr = orderBy.split(",");
        String[] isAscArr = orderRules.split(",");
        if (isAscArr.length != 1 && isAscArr.length != orderByArr.length) {
            throw new ServiceException("排序参数有误");
        }

        List<OrderItem> list = new ArrayList<>();
        // 每个字段各自排序
        for (int i = 0; i < orderByArr.length; i++) {
            String orderByStr = orderByArr[i];
            String isAscStr = isAscArr.length == 1 ? isAscArr[0] : isAscArr[i];
            if ("asc".equals(isAscStr)) {
                list.add(OrderItem.asc(orderByStr));
            } else if ("desc".equals(isAscStr)) {
                list.add(OrderItem.desc(orderByStr));
            } else {
                throw new ServiceException("排序参数有误");
            }
        }
        return list;
    }

}
