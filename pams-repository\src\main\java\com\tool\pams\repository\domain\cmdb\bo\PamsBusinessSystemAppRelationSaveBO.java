package com.tool.pams.repository.domain.cmdb.bo;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 业务系统与应用关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 15:56:15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "PamsBusinessSystemAppRelationSaveBO对象", description = "业务系统与应用关联表")
public class PamsBusinessSystemAppRelationSaveBO implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "业务系统id")
    private Long businessSystemId;


    @Schema(description = "应用id")
    private Long appId;


}
