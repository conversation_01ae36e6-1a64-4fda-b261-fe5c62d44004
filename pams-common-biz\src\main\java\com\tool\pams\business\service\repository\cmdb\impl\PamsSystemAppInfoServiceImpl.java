package com.tool.pams.business.service.repository.cmdb.impl;

import com.tool.pams.repository.domain.cmdb.bo.PamsSystemAppInfoSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsSystemAppInfoUpdateBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsSystemAppInfoQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.vo.PamsSystemAppInfoDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsSystemAppInfoPageVO;
import com.tool.pams.repository.domain.cmdb.db.PamsSystemAppInfoDO;
import com.tool.pams.repository.mapper.cmdb.PamsSystemAppInfoMapper;
import com.tool.pams.business.service.repository.cmdb.PamsSystemAppInfoService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
/**
 * <p>
 * 系统应用信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 11:13:24
 */
@Slf4j
@Service
public class PamsSystemAppInfoServiceImpl extends ServiceImpl<PamsSystemAppInfoMapper, PamsSystemAppInfoDO> implements PamsSystemAppInfoService {

    @Resource
    private PamsSystemAppInfoMapper pamsSystemAppInfoMapper;

    @Override
    public Boolean saveInfo(PamsSystemAppInfoSaveBO saveBO){
        PamsSystemAppInfoDO entity = new PamsSystemAppInfoDO();
        BeanUtils.copyProperties(saveBO, entity);
        return SqlHelper.retBool(pamsSystemAppInfoMapper.insert(entity));
    }

    @Override
    public Boolean delInfo(Long id){
        return SqlHelper.retBool(pamsSystemAppInfoMapper.deleteById(id));
    }

    @Override
    public Boolean updateInfo(PamsSystemAppInfoUpdateBO updateBO){
        PamsSystemAppInfoDO entity = new PamsSystemAppInfoDO();
        BeanUtils.copyProperties(updateBO, entity);
        return SqlHelper.retBool(pamsSystemAppInfoMapper.updateById(entity));
    }

    @Override
    public PamsSystemAppInfoDetailVO getInfo(Long id){
        PamsSystemAppInfoDO entity = pamsSystemAppInfoMapper.selectById(id);
        return PamsSystemAppInfoDetailVO.of(entity);
    }

    @Override
    public IPage<PamsSystemAppInfoPageVO> getPageInfo(PamsSystemAppInfoQueryParamsBO queryParamsBO){
        return pamsSystemAppInfoMapper.selectPage(queryParamsBO.pageInfo(), queryParamsBO.queryWrapper()).convert(PamsSystemAppInfoPageVO::of);
    }

}
