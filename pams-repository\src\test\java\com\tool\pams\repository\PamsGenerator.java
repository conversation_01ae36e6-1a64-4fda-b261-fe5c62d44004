package com.tool.pams.repository;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.ConstVal;
import com.baomidou.mybatisplus.generator.config.TemplateType;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.fill.Column;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.Scanner;

import static com.baomidou.mybatisplus.generator.config.rules.DbColumnType.LOCAL_DATE_TIME;

/**
 * <AUTHOR>
 * @description: Mysql数据库代码生成器
 * @date 2022/9/18 16:27
 */
public class PamsGenerator {

    /**
     * 数据库url
     */
    public static final String URL = "****************************************************************************************************************************************************************";
    /**
     * 数据库用户名
     */
    public static final String USERNAME = "u_pams";
    /**
     * 数据库密码
     */
    public static final String PASSWORD = "Puincn$cnsijf3mchnfei232";
    /**
     * 作者信息
     */
    private static final String AUTHOR = "system";
    /**
     * 生成文件基础路径
     */
    private static final String BASE_PATH = PamsGenerator.class.getClassLoader().getResource("").getPath();
    /**
     * 生成类路径
     */
    private static final String CLASS_PATH = getRealFilePath(BASE_PATH + "../../db/generator/src/main/java/");

    /**
     * 基础包名
     */
    private static final String BASE_PACKAGE_NAME = "com.tool.pams";

    /**
     * 表前缀
     */
    private static final String TABLE_PREFIX = "t_";

    /**
     * 插入更新参数类忽略的LocalDateTime字段
     */
    public static String[] ignoreTimeField = {"create_time", "update_time"};

    public static void main(String[] args) {

        Scanner s = new Scanner(System.in);
        System.out.println("请输入模块名称：");
        String modelName = s.nextLine();
        FastAutoGenerator fastAutoGenerator = FastAutoGenerator.create(URL, USERNAME, PASSWORD)
                // 全局配置
                .globalConfig(builder -> {
                    builder.author(AUTHOR) // 设置作者
                            .commentDate("yyyy-MM-dd HH:mm:ss") // 注释日期
                            .dateType(DateType.TIME_PACK) // 定义生成的实体类中日期的类型 TIME_PACK=LocalDateTime;ONLY_DATE=Date;
                            .disableOpenDir() // //禁止打开输出目录，默认打开
                            .enableSwagger() // 开启 swagger 模式
                            .outputDir(CLASS_PATH); // 指定输出目录
                })
                .packageConfig((scanner, builder) -> {
                    builder.parent(BASE_PACKAGE_NAME) // 设置父包名
                            .entity("repository.domain." + modelName + ".db")
                            .xml("xml.mapper." + modelName)
                            .other("repository")
                            .mapper("repository.mapper." + modelName)
                            .service("business.service.repository." + modelName)
                            .serviceImpl("business.service.repository." + modelName + ".impl")
                            .controller("api.web.controller." + modelName);
                })
                .strategyConfig((scanner, builder) -> {
                    builder.addInclude(scanner.apply("请输入表名，多个英文逗号分割：").split(","))
                            .addTablePrefix(TABLE_PREFIX)
                            //controller 配置
                            .controllerBuilder()
                            .formatFileName("%sController")
                            .enableRestStyle()
                            .fileOverride()
                            //service 配置
                            .serviceBuilder()
                            .formatServiceFileName("%sService")
                            .formatServiceImplFileName("%sServiceImpl")
                            .fileOverride()
                            //entity 配置
                            .entityBuilder()
                            .formatFileName("%sDO")
                            .fileOverride()
                            .enableLombok() //开启 Lombok
                            .naming(NamingStrategy.underline_to_camel) // 数据库表映射到实体的命名策略：下划线转驼峰命
                            .columnNaming(NamingStrategy.underline_to_camel) // 数据库表字段映射到实体的命名策略：下划线转驼峰命
                            .addTableFills(
                                    new Column("create_time", FieldFill.INSERT),
                                    new Column("creator", FieldFill.INSERT),
                                    new Column("deleted", FieldFill.INSERT),
                                    new Column("update_time", FieldFill.INSERT_UPDATE),
                                    new Column("updater", FieldFill.INSERT_UPDATE))
                            .logicDeleteColumnName("deleted") // 逻辑删除字段名
                            .enableTableFieldAnnotation()
                            //mapper 配置
                            .mapperBuilder()
                            .superClass(BaseMapper.class)
                            .formatMapperFileName("%sMapper")
                            .enableMapperAnnotation()
                            .fileOverride()
                            .enableBaseColumnList()
                            .enableBaseResultMap()
                            .build();
                }).templateConfig(builder -> {
                    builder.disable(TemplateType.ENTITY)
                            .entity("/templates/entity.java")
                            .service("/templates/service.java")
                            .serviceImpl("/templates/serviceImpl.java")
                            .mapper("/templates/mapper.java")
                            .controller("/templates/controller.java")
                            .build();
                }).injectionConfig(consumer -> {
                    consumer.beforeOutputFile((tableInfo, objectMap) -> {
                        String className = tableInfo.getEntityName().replace("DO", "");
                        String saveBO = className + "SaveBO";
                        String updateBO = className + "UpdateBO";
                        String queryParamsBO = className + "QueryParamsBO";
                        String detailVO = className + "DetailVO";
                        String pageVO = className + "PageVO";
                        String boPath = "/domain/" + replaceCloudProvider(modelName) + "/bo/";
                        String voPath = "/domain/" + replaceCloudProvider(modelName) + "/vo/";
                        String modulePackageName = ((Map<String, String>) objectMap.get("package")).get(ConstVal.OTHER);
                        objectMap.put("boPath", modulePackageName + ".domain." + modelName + ".bo");
                        objectMap.put("voPath", modulePackageName + ".domain." + modelName + ".vo");
                        objectMap.put("saveBO", saveBO);
                        objectMap.put("updateBO", updateBO);
                        objectMap.put("queryParamsBO", queryParamsBO);
                        objectMap.put("detailVO", detailVO);
                        objectMap.put("pageVO", pageVO);
                        objectMap.put("className", className);
                        boolean existOtherLocalDateTimeField = tableInfo.getFields().stream().anyMatch(item ->
                                item.getColumnType().getType().equals(LOCAL_DATE_TIME.getType()) && !StringUtils.equalsAny(item.getColumnName(), ignoreTimeField));
                        objectMap.put("existOtherLocalDateTimeField", existOtherLocalDateTimeField);
                        Map<String, String> customFile = new HashMap<>(10);
                        customFile.put(boPath + saveBO + ".java", "/templates/save_bo_entity.java.vm");
                        customFile.put(boPath + updateBO + ".java", "/templates/update_bo_entity.java.vm");
                        customFile.put(boPath + queryParamsBO + ".java", "/templates/query_params_bo_entity.java.vm");
                        customFile.put(voPath + detailVO + ".java", "/templates/detail_vo_entity.java.vm");
                        customFile.put(voPath + pageVO + ".java", "/templates/page_vo_entity.java.vm");
                        consumer.customFile(customFile);
                    });
                }).templateEngine(new MyVelocityTemplateEngine());
        fastAutoGenerator.execute();


    }

    /**
     * 获取路径
     *
     * @param path
     * @return
     */
    private static String getRealFilePath(String path) {
        return path.replace("/", File.separator).replace("\\", File.separator);
    }

    /**
     * 将指定的字符串中的 "cloud.provider" 替换为 "cloud/provider"
     *
     * @param input 需要进行替换的原始字符串
     * @return 替换后的字符串
     */
    public static String replaceCloudProvider(String input) {
        if (input == null) {
            return null;
        }
        return input.replace(".", "/");
    }
}
