package com.tool.pams.business.service.repository.cmdb;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemInfoDO;
import com.tool.pams.repository.domain.cmdb.bo.PamsBusinessSystemInfoSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsBusinessSystemInfoUpdateBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsBusinessSystemInfoQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.vo.PamsBusinessSystemInfoDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsBusinessSystemInfoPageVO;

import java.util.List;

/**
 * <p>
 * 新业务系统资源信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 11:13:24
 */
public interface PamsBusinessSystemInfoService extends IService<PamsBusinessSystemInfoDO> {

    /**
     * 添加信息
     *
     * @param saveBO
     * @return
     */
    Boolean saveInfo(PamsBusinessSystemInfoSaveBO saveBO);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delInfo(Long id);

    /**
     * 修改信息
     *
     * @param updateBO
     * @return
     */
    Boolean updateInfo(PamsBusinessSystemInfoUpdateBO updateBO);

    /**
     * 通过ID获取信息
     *
     * @param id
     * @return
     */
    PamsBusinessSystemInfoDetailVO getInfo(Long id);

    /**
     * 分页获取列表
     *
     * @param queryParamsBO
     * @return
     */
    IPage<PamsBusinessSystemInfoPageVO> getPageInfo(PamsBusinessSystemInfoQueryParamsBO queryParamsBO);

}
