package com.tool.pams.repository.mapper.cmdb;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemAppRelationDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 业务系统与应用关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 11:13:24
 */
@Mapper
public interface PamsBusinessSystemAppRelationMapper extends BaseMapper<PamsBusinessSystemAppRelationDO> {

    /**
     * 同步应用
     *
     * @return
     */
    @DS("cmdb")
    List<PamsBusinessSystemAppRelationDO> syncPamsBusinessSystemAppRelation();

    /**
     * 批量插入或更新业务系统与应用关联信息（使用INSERT ... ON DUPLICATE KEY UPDATE）
     *
     * @param records 业务系统与应用关联信息列表
     * @return 受影响的行数
     */
    int insertOrUpdateBatch(@Param("records") List<PamsBusinessSystemAppRelationDO> records);

}
