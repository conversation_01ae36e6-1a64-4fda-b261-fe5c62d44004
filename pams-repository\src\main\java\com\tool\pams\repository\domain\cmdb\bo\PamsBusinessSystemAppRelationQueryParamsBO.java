package com.tool.pams.repository.domain.cmdb.bo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.pams.repository.domain.common.PageParamsBO;
import com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemAppRelationDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 业务系统与应用关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 15:56:15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "PamsBusinessSystemAppRelationQueryParamsBO对象", description = "业务系统与应用关联表")
public class PamsBusinessSystemAppRelationQueryParamsBO extends PageParamsBO<PamsBusinessSystemAppRelationDO> implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "唯一ID")
    private Long id;

    @Schema(description = "业务系统id")
    private Long businessSystemId;

    @Schema(description = "应用id")
    private Long appId;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "修改时间-开始")
    private LocalDateTime updateTimeStart;

    @Schema(description = "修改时间-结束")
    private LocalDateTime updateTimeEnd;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Override
    public LambdaQueryWrapper<PamsBusinessSystemAppRelationDO> queryWrapper() {

        LambdaQueryWrapper<PamsBusinessSystemAppRelationDO> query = new LambdaQueryWrapper<>();

        query.eq(id!=null,PamsBusinessSystemAppRelationDO::getId,id);

        query.eq(businessSystemId!=null,PamsBusinessSystemAppRelationDO::getBusinessSystemId,businessSystemId);

        query.eq(appId!=null,PamsBusinessSystemAppRelationDO::getAppId,appId);

        query.ge(createTimeStart != null, PamsBusinessSystemAppRelationDO::getCreateTime, createTimeStart);

        query.le(createTimeEnd != null, PamsBusinessSystemAppRelationDO::getCreateTime, createTimeEnd);

        query.ge(updateTimeStart != null, PamsBusinessSystemAppRelationDO::getUpdateTime, updateTimeStart);

        query.le(updateTimeEnd != null, PamsBusinessSystemAppRelationDO::getUpdateTime, updateTimeEnd);

        query.eq(StringUtils.isNotBlank(creator),PamsBusinessSystemAppRelationDO::getCreator,creator);

        query.eq(StringUtils.isNotBlank(updater),PamsBusinessSystemAppRelationDO::getUpdater,updater);

        query.eq(deleted!=null,PamsBusinessSystemAppRelationDO::getDeleted,deleted);

        return query;
    }
}
