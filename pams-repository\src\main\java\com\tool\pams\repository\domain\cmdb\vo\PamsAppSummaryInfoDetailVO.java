package com.tool.pams.repository.domain.cmdb.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.tool.pams.repository.domain.cmdb.db.PamsAppSummaryInfoDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <p>
 * 应用信息汇总表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "PamsAppSummaryInfoDetailVO对象", description = "应用信息汇总表")
public class PamsAppSummaryInfoDetailVO implements Serializable{

    private static final long serialVersionUID = 1L;
    
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "应用ID（对应t_pams_system_app_info的id，parent_app_id=0的父级节点）")
    private Long appId;

    @Schema(description = "应用名称")
    private String appName;

    @Schema(description = "状态（start=启用/stop=下架/maintain=维护中）")
    private String status;

    @Schema(description = "类型（app=APP/frontend=前端/backend=后端，手动编辑）")
    private String type;

    @Schema(description = "所属机构ID（手动编辑，关联查询）")
    private Long organizationId;

    @Schema(description = "模块数量")
    private Integer moduleCount;

    @Schema(description = "总发布次数")
    private Integer totalPublishCount;

    @Schema(description = "总发布时长（毫秒）")
    private Long totalPublishDuration;

    @Schema(description = "平均发布时长（毫秒）")
    private Long avgPublishDuration;

    @Schema(description = "平均启动时长（毫秒）")
    private Long avgStartupDuration;

    @Schema(description = "负责开发组")
    private String devTeam;

    @Schema(description = "生产应用标识")
    private String prodAppIdentifier;

    @Schema(description = "git地址")
    private String gitAddress;

    @Schema(description = "开发语言")
    private String developLanguage;

    @Schema(description = "负责测试组（手动编辑）")
    private String testTeam;

    @Schema(description = "负责项目经理（手动编辑）")
    private String projectManager;

    @Schema(description = "应用大类（手动编辑）")
    private String appMajorCategory;

    @Schema(description = "应用小类（手动编辑）")
    private String appMinorCategory;

    @Schema(description = "SSO系统标识（手动编辑）")
    private String ssoSystemFlag;

    @Schema(description = "源码工程标识（手动编辑）")
    private String sourceProjectFlag;

    @Schema(description = "数据库标识（手动编辑）")
    private String databaseFlag;

    @Schema(description = "OSS私有文件桶标识（手动编辑）")
    private String ossPrivateBucketFlag;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Schema(description = "机构名称")
    private String organizationName;

    @Schema(description = "产品名称列表")
    private String productNames;

    public static PamsAppSummaryInfoDetailVO of(PamsAppSummaryInfoDO entity){
        if(entity == null){
            return null;
        }
        PamsAppSummaryInfoDetailVO detailVO = new PamsAppSummaryInfoDetailVO();
        BeanUtils.copyProperties(entity,detailVO);
        return detailVO;
    }
}
