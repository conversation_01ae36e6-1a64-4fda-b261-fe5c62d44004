package ${package.Controller};

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import com.hzed.structure.log.annotation.PrintLog;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;
import com.hzed.structure.tool.annotation.ApiResponse;
import ${package.Service}.${table.serviceName};
import ${boPath}.${saveBO};
import ${boPath}.${updateBO};
import ${boPath}.${queryParamsBO};
import ${voPath}.${detailVO};
import ${voPath}.${pageVO};

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * $!{table.comment} 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
@Tag(name = "$!{table.comment}")
@RestController
@RequestMapping("/${className.substring(0, 1).toLowerCase()}${className.substring(1)}")
public class ${table.controllerName} {

    @Resource
    private ${table.serviceName} ${table.serviceName.substring(0, 1).toLowerCase()}${table.serviceName.substring(1)};

    @ApiResponse
    @Operation(summary = "根据id查询$!{table.comment}")
    @PrintLog("根据id查询$!{table.comment}")
    @GetMapping("/info/{id}")
    public ${detailVO} info(@PathVariable("id") Long id) {
        return ${table.serviceName.substring(0, 1).toLowerCase()}${table.serviceName.substring(1)}.getInfo(id);
    }

    @ApiResponse
    @Operation(summary = "分页查询$!{table.comment}")
    @PrintLog("分页查询$!{table.comment}")
    @GetMapping("/page")
    public IPage<${pageVO}> page(@Valid @ParameterObject ${queryParamsBO} queryParamsBO) {
        return ${table.serviceName.substring(0, 1).toLowerCase()}${table.serviceName.substring(1)}.getPageInfo(queryParamsBO);
    }

    @ApiResponse
    @Operation(summary = "保存$!{table.comment}")
    @PrintLog("保存$!{table.comment}")
    @PostMapping("/save")
    public Boolean save(@RequestBody @Valid ${saveBO} saveBO) {
        return ${table.serviceName.substring(0, 1).toLowerCase()}${table.serviceName.substring(1)}.saveInfo(saveBO);
    }

    @ApiResponse
    @Operation(summary = "修改$!{table.comment}")
    @PrintLog("修改$!{table.comment}")
    @PostMapping("/update")
    public Boolean update(@RequestBody @Valid ${updateBO} updateBO) {
        return ${table.serviceName.substring(0, 1).toLowerCase()}${table.serviceName.substring(1)}.updateInfo(updateBO);
    }

    @ApiResponse
    @Operation(summary = "删除$!{table.comment}")
    @PrintLog("删除$!{table.comment}")
    @PostMapping("/del/{id}")
    public Boolean del(@PathVariable("id") Long id) {
        return ${table.serviceName.substring(0, 1).toLowerCase()}${table.serviceName.substring(1)}.delInfo(id);
    }
}
