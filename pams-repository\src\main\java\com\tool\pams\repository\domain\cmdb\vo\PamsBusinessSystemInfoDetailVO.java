package com.tool.pams.repository.domain.cmdb.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemInfoDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * 新业务系统资源信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 11:13:24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "PamsBusinessSystemInfoDetailVO对象", description = "新业务系统资源信息表")
public class PamsBusinessSystemInfoDetailVO implements Serializable{

    private static final long serialVersionUID = 1L;
    
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "系统编码")
    private String systemNo;

    @Schema(description = "系统名称")
    private String systemName;

    @Schema(description = "系统代号")
    private String systemCode;

    @Schema(description = "系统类别")
    private String systemType;

    @Schema(description = "账号ID")
    private String accountId;

    @Schema(description = "负责人")
    private String director;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    public static PamsBusinessSystemInfoDetailVO of(PamsBusinessSystemInfoDO entity){
        if(entity == null){
            return null;
        }
        PamsBusinessSystemInfoDetailVO detailVO = new PamsBusinessSystemInfoDetailVO();
        BeanUtils.copyProperties(entity,detailVO);
        return detailVO;
    }

}
