package com.tool.pams.repository.domain.system.vo;


import com.tool.pams.repository.domain.system.db.SysDictKeyDO;
import com.tool.pams.repository.domain.system.db.SysDictValueDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <p>
 * 字典字典
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-11 11:38:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SysDictKeyDetailVO对象", description = "字典字典")
public class SysDictKeyDetailVO implements Serializable{

    private static final long serialVersionUID = 1L;
    
    @Schema(description = "id")
    private Long id;

    @Schema(description = "字典编码")
    private String keyName;

    @Schema(description = "字典名称")
    private String keyLabel;

    @Schema(description = "字典描述")
    private String description;

    @Schema(description = "字典值列表")
    private List<SysDictValueDO> dictValueList;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    public static SysDictKeyDetailVO of(SysDictKeyDO entity){
        if(entity == null){
            return null;
        }
        SysDictKeyDetailVO detailVO = new SysDictKeyDetailVO();
        BeanUtils.copyProperties(entity,detailVO);
        return detailVO;
    }

}
